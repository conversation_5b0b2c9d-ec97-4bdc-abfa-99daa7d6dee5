const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase credentials in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testCRUD() {
  try {
    console.log('🧪 Testing CRUD operations...')
    
    // Test 1: Read products (should work)
    console.log('\n1. Testing READ products...')
    const { data: products, error: readError } = await supabase
      .from('products')
      .select('*')
      .limit(3)
    
    if (readError) {
      console.error('❌ Read error:', readError.message)
    } else {
      console.log('✅ Read successful:', products.length, 'products found')
    }
    
    // Test 2: Try to create a product (should fail without auth)
    console.log('\n2. Testing CREATE product (without auth)...')
    const { data: newProduct, error: createError } = await supabase
      .from('products')
      .insert({
        slug: 'test-product-' + Date.now(),
        name: 'Test Product',
        description: 'Test description',
        brand: 'Test Brand',
        price_cents: 10000
      })
      .select()
      .single()
    
    if (createError) {
      console.error('❌ Create error (expected):', createError.message)
    } else {
      console.log('✅ Create successful (unexpected):', newProduct)
    }
    
    // Test 3: Try to create a category (should fail without auth)
    console.log('\n3. Testing CREATE category (without auth)...')
    const { data: newCategory, error: categoryError } = await supabase
      .from('categories')
      .insert({
        slug: 'test-category-' + Date.now(),
        name: 'Test Category'
      })
      .select()
      .single()
    
    if (categoryError) {
      console.error('❌ Category create error (expected):', categoryError.message)
    } else {
      console.log('✅ Category create successful (unexpected):', newCategory)
    }
    
    // Test 4: Try to sign up a user
    console.log('\n4. Testing user signup...')
    const testEmail = `test-${Date.now()}@example.com`
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: testEmail,
      password: 'testpassword123',
      options: {
        emailRedirectTo: undefined // Disable email confirmation
      }
    })
    
    if (signUpError) {
      console.error('❌ Signup error:', signUpError.message)
    } else {
      console.log('✅ Signup successful:', signUpData.user?.email)
      
      // Test 5: Try CRUD with authenticated user
      if (signUpData.user) {
        console.log('\n5. Testing CREATE with authenticated user...')
        
        const { data: authProduct, error: authCreateError } = await supabase
          .from('products')
          .insert({
            slug: 'auth-test-product-' + Date.now(),
            name: 'Auth Test Product',
            description: 'Test with auth',
            brand: 'Auth Brand',
            price_cents: 15000
          })
          .select()
          .single()
        
        if (authCreateError) {
          console.error('❌ Auth create error:', authCreateError.message)
        } else {
          console.log('✅ Auth create successful:', authProduct.name)
          
          // Clean up - try to delete
          const { error: deleteError } = await supabase
            .from('products')
            .delete()
            .eq('id', authProduct.id)
          
          if (deleteError) {
            console.error('❌ Delete error:', deleteError.message)
          } else {
            console.log('✅ Delete successful')
          }
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

// Run the test
if (require.main === module) {
  testCRUD()
    .then(() => process.exit(0))
    .catch(() => process.exit(1))
}

module.exports = { testCRUD }
