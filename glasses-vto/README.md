# GlassVTO - Virtual Glasses Try-On Application

A modern web application for virtual glasses try-on using advanced AR technology with MediaPipe face detection and Three.js rendering.

## 🚀 Features

- **Real-time Virtual Try-On**: Try glasses using your camera with accurate face tracking
- **Product Catalog**: Browse and filter glasses by gender, shape, color, and price
- **MediaPipe Face Mesh**: Advanced face detection with 468 landmark points
- **Three.js 3D Rendering**: Realistic glasses overlay with proper positioning
- **Snapshot Feature**: Capture and save your look
- **Responsive Design**: Works on desktop and mobile devices
- **Privacy-First**: All processing happens locally on your device

## 🛠 Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS + shadcn/ui
- **3D Rendering**: Three.js
- **Face Detection**: MediaPipe Face Mesh
- **Database**: Supabase (PostgreSQL)
- **State Management**: Zustand
- **Authentication**: Supabase Auth

## 📋 Prerequisites

- Node.js 18+ and npm
- Supabase account (for database and auth)
- Webcam/camera for try-on feature

## 🔧 Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/glasses-vto.git
cd glasses-vto
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
Create a `.env.local` file in the root directory:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. Set up Supabase:
   - Create a new project on [Supabase](https://supabase.com)
   - Run the SQL migrations from `supabase/migrations/001_initial_schema.sql`
   - Copy your project URL and anon key to `.env.local`

5. Add 3D models:
   - Place your glasses 3D models (.glb/.gltf files) in `public/models/`
   - Update product records in database with model URLs

## 🚀 Development

Run the development server:
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
glasses-vto/
├── src/
│   ├── app/              # Next.js app router pages
│   ├── components/       # React components
│   │   ├── catalog/      # Product catalog components
│   │   ├── layout/       # Layout components
│   │   ├── try-on/       # Try-on feature components
│   │   └── ui/           # shadcn/ui components
│   ├── lib/              # Utilities and helpers
│   │   ├── mediapipe/    # Face detection logic
│   │   ├── three/        # 3D rendering logic
│   │   └── supabase/     # Database clients
│   └── types/            # TypeScript type definitions
├── public/
│   ├── models/           # 3D glasses models
│   └── images/           # Static images
└── supabase/
    └── migrations/       # Database migrations
```

## 🎯 Usage

1. **Browse Catalog**: Visit `/catalog` to see all available glasses
2. **Filter Products**: Use sidebar filters to narrow down choices
3. **Virtual Try-On**: Click "Try On" on any product
4. **Grant Camera Access**: Allow browser to use your camera
5. **Adjust Settings**: Use sliders to adjust brightness and size
6. **Take Snapshot**: Capture and download your look

## 🔒 Privacy

- All face detection and AR processing happens locally in your browser
- Camera feed is never transmitted to servers
- Snapshots are saved locally unless explicitly shared

## 📝 Database Schema

The application uses the following main tables:
- `products`: Glasses catalog information
- `product_variants`: Color/style variations
- `wishlists`: User saved items
- `try_on_history`: User try-on sessions

## 🚢 Deployment

1. Build the application:
```bash
npm run build
```

2. Deploy to Vercel (recommended):
```bash
vercel
```

Or deploy to any platform that supports Next.js.

## ⚠️ Known Issues

- MediaPipe requires HTTPS for camera access (use ngrok for local testing)
- Some browsers may have WebGL compatibility issues
- Mobile performance depends on device capabilities

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- MediaPipe team for face detection technology
- Three.js community for 3D rendering capabilities
- Supabase for backend infrastructure
- shadcn/ui for beautiful components
