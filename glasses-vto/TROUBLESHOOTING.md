# 🔧 Virtual Try-On Troubleshooting Guide

## Common Issues and Solutions

### 1. **MediaPipe Face Detection Error**

#### **Error:** `detectForVideo` fails with invalid timestamp
```
Face detection error: Invalid timestamp
```

#### **Solutions:**
1. **Check Video Readiness:**
   ```javascript
   // Ensure video is ready before detection
   if (video.readyState < video.HAVE_ENOUGH_DATA) return null
   if (video.videoWidth === 0 || video.videoHeight === 0) return null
   ```

2. **Reset Timestamp:**
   ```javascript
   // Reset timestamp on error
   this.lastVideoTime = -1
   ```

3. **Use CPU Delegate:**
   ```javascript
   // Change from GPU to CPU for better compatibility
   delegate: 'CPU'
   ```

### 2. **Glasses Not Visible**

#### **Possible Causes:**
- Camera positioning incorrect
- Model not loaded
- Face not detected
- Coordinate mapping wrong

#### **Debug Steps:**
1. **Check Console Logs:**
   ```
   MediaPipe FaceLandmarker initialized successfully
   Detected 1 face(s)
   Glasses rendered at position: Vector3 {x: 320, y: -240, z: 100}
   ```

2. **Verify Model Loading:**
   ```javascript
   console.log('Glasses model loaded:', this.glassesModel)
   console.log('Model visible:', this.glassesModel.visible)
   ```

3. **Check Camera Setup:**
   ```javascript
   console.log('Camera position:', this.camera.position)
   console.log('Camera looking at:', this.camera.getWorldDirection())
   ```

### 3. **Video Not Ready**

#### **Error:** Video dimensions are 0 or readyState < 2
```
Video not ready for detection
Video dimensions not available
```

#### **Solutions:**
1. **Wait for Metadata:**
   ```javascript
   await new Promise((resolve) => {
     if (video.readyState >= 1) {
       resolve(true)
     } else {
       video.addEventListener('loadedmetadata', () => resolve(true), { once: true })
     }
   })
   ```

2. **Check Video Stream:**
   ```javascript
   console.log('Video dimensions:', video.videoWidth, 'x', video.videoHeight)
   console.log('Video ready state:', video.readyState)
   ```

### 4. **Three.js Rendering Issues**

#### **Common Problems:**
- Canvas not visible
- Transparent background not working
- Lighting too dark/bright

#### **Solutions:**
1. **Check Canvas Setup:**
   ```javascript
   renderer.setClearColor(0x000000, 0) // Transparent
   renderer.setSize(width, height)
   ```

2. **Verify Lighting:**
   ```javascript
   const frontLight = new THREE.SpotLight(0xffffff, 0.3)
   const backLight = new THREE.SpotLight(0xffffff, 0.3)
   ```

3. **Camera Position:**
   ```javascript
   // Benson Ruan's method
   camera.position.x = width / 2
   camera.position.y = -height / 2
   camera.position.z = -(height / 2) / Math.tan(45 / 2)
   ```

### 5. **Coordinate System Issues**

#### **Problem:** Glasses appear in wrong position
#### **Solution:** Use Benson Ruan's direct mapping
```javascript
// Direct coordinate mapping
worldX = midEye.x * videoWidth
worldY = -(midEye.y * videoHeight)
worldZ = -camera.position.z + midEye.z * 100
```

### 6. **Performance Issues**

#### **Symptoms:**
- Slow frame rate
- Browser freezing
- High CPU usage

#### **Solutions:**
1. **Limit Frame Rate:**
   ```javascript
   if (timestamp - this.lastVideoTime < 16) return null // ~60fps
   ```

2. **Optimize MediaPipe:**
   ```javascript
   outputFaceBlendshapes: false // Disable for performance
   numFaces: 1 // Limit to single face
   ```

3. **Use CPU Delegate:**
   ```javascript
   delegate: 'CPU' // More stable than GPU
   ```

## Debug Checklist

### **Before Starting Camera:**
- [ ] MediaPipe model loaded successfully
- [ ] Three.js renderer initialized
- [ ] Canvas element available
- [ ] No console errors

### **After Starting Camera:**
- [ ] Video stream active
- [ ] Video dimensions > 0
- [ ] Video readyState >= 2
- [ ] Face detection working
- [ ] Glasses model loaded

### **During Detection:**
- [ ] Face landmarks detected
- [ ] Key points calculated
- [ ] Rotation/scale computed
- [ ] Position updated
- [ ] Glasses rendered

## Console Commands for Debugging

### **Check MediaPipe Status:**
```javascript
// In browser console
console.log('Face detector initialized:', faceDetectorRef.current?.isInitialized)
```

### **Check Three.js Scene:**
```javascript
// Check scene objects
console.log('Scene children:', glassesRendererRef.current?.scene.children)
console.log('Glasses model:', glassesRendererRef.current?.glassesModel)
```

### **Check Video Status:**
```javascript
// Check video element
const video = document.querySelector('video')
console.log('Video ready:', video.readyState)
console.log('Video dimensions:', video.videoWidth, 'x', video.videoHeight)
```

## Browser Compatibility

### **Recommended Browsers:**
- ✅ Chrome 90+ (Best performance)
- ✅ Firefox 88+
- ✅ Safari 14+ (iOS requires HTTPS)
- ⚠️ Edge 90+ (Some issues with MediaPipe)

### **Required Features:**
- WebGL 2.0
- WebAssembly
- getUserMedia API
- WebRTC

## Common Error Messages

### **"Failed to initialize MediaPipe"**
- Check internet connection
- Try CPU delegate instead of GPU
- Clear browser cache

### **"Video not ready for detection"**
- Wait for video metadata to load
- Check camera permissions
- Verify video stream is active

### **"Face detection error"**
- Reset timestamp counter
- Check video element validity
- Try reinitializing MediaPipe

### **"Glasses not visible"**
- Check model loading
- Verify camera position
- Debug coordinate mapping

## Performance Optimization

### **For Low-End Devices:**
```javascript
// Reduce detection frequency
if (timestamp - this.lastVideoTime < 33) return null // ~30fps

// Use smaller video resolution
video: {
  width: { ideal: 640 },
  height: { ideal: 480 }
}

// Disable advanced features
outputFaceBlendshapes: false
```

### **For High-End Devices:**
```javascript
// Higher detection frequency
if (timestamp - this.lastVideoTime < 8) return null // ~120fps

// Use higher video resolution
video: {
  width: { ideal: 1280 },
  height: { ideal: 720 }
}
```

## Getting Help

### **Debug Information to Collect:**
1. Browser version and OS
2. Console error messages
3. Video dimensions and readyState
4. MediaPipe initialization status
5. Three.js scene information

### **Steps to Report Issues:**
1. Open browser developer tools
2. Go to Console tab
3. Reproduce the issue
4. Copy all error messages
5. Include browser/OS information

---

*This troubleshooting guide covers the most common issues with the virtual try-on system. For additional help, check the browser console for specific error messages.*
