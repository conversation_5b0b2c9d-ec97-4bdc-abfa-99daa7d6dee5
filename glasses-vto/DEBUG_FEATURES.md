# Debug Features untuk Try-On System

## Overview
Sistem debug telah berhasil diterapkan ke komponen Try-On dengan berbagai fitur monitoring dan troubleshooting yang membantu dalam development dan testing.

## Fitur Debug yang Diterapkan

### 1. Debug Mode Toggle
- **Lokasi**: <PERSON><PERSON> "Debug ON/OFF" di kontrol Try-On
- **Fungsi**: Mengaktifkan/menonaktifkan logging debug yang detail
- **Penggunaan**: Klik tombol untuk toggle debug mode

### 2. Debug Overlay
- **Lokasi**: Overlay di atas video feed (pojok kiri atas)
- **Fungsi**: Menampilkan statistik real-time
- **Data yang ditampilkan**:
  - FPS (Frames Per Second)
  - Jumlah wajah yang terdeteksi
  - <PERSON><PERSON><PERSON> render yang dilakukan
  - Jumlah error yang terjadi
  - Status kamera (ON/OFF)
  - Status deteksi (YES/NO)
  - Waktu deteksi terakhir

### 3. Console Debug Methods
Metode debug yang tersedia melalui `window.debugGlasses`:

#### Positioning Tests
- `window.debugGlasses.testCenter()` - Test posisi tengah
- `window.debugGlasses.testFace(x, y)` - Test posisi wajah (x, y normalized 0-1)
- `window.debugGlasses.testManual(x, y, z, scale)` - Test posisi manual

#### Information Getters
- `window.debugGlasses.getCamera()` - Dapatkan posisi kamera
- `window.debugGlasses.getGlasses()` - Dapatkan posisi kacamata
- `window.debugGlasses.getStats()` - Dapatkan statistik debug

#### Control Methods
- `window.debugGlasses.toggleDebug()` - Toggle debug mode
- `window.debugGlasses.toggleOverlay()` - Toggle debug overlay
- `window.debugGlasses.resetStats()` - Reset statistik debug

### 4. Enhanced Logging
- **FPS Monitoring**: Tracking frame rate real-time
- **Face Detection Logging**: Log setiap deteksi wajah
- **Error Tracking**: Pencatatan dan counting error
- **Render Monitoring**: Tracking jumlah render calls

### 5. Debug Test Page
- **URL**: `/debug`
- **Komponen**: `TryOnDebugTest`
- **Fitur**:
  - Test berbagai model kacamata
  - Tombol untuk menjalankan debug commands
  - Instruksi penggunaan yang lengkap
  - Integrasi dengan Try-On component

## Cara Menggunakan Debug Features

### Langkah-langkah Testing:

1. **Buka halaman debug**:
   ```
   http://localhost:3001/debug
   ```

2. **Aktifkan kamera**:
   - Klik tombol "Start Camera"
   - Izinkan akses kamera

3. **Aktifkan debug mode**:
   - Klik tombol "Debug ON"
   - Klik tombol "Overlay" untuk melihat statistik

4. **Test positioning**:
   - Gunakan debug commands di halaman debug
   - Atau jalankan commands di browser console

5. **Monitor performance**:
   - Lihat FPS di debug overlay
   - Check console untuk detailed logs
   - Monitor error count

### Debug Commands Examples:

```javascript
// Test center positioning
window.debugGlasses.testCenter()

// Test face position at center
window.debugGlasses.testFace(0.5, 0.4)

// Manual positioning
window.debugGlasses.testManual(320, 0, -100, 30)

// Get current positions
console.log('Camera:', window.debugGlasses.getCamera())
console.log('Glasses:', window.debugGlasses.getGlasses())

// Get debug statistics
console.log('Stats:', window.debugGlasses.getStats())
```

## Expected Behavior

### Normal Operation:
- FPS: 30-60 fps (tergantung device)
- Face Count: 1 (saat wajah terdeteksi)
- Errors: 0 atau minimal
- Camera: ON
- Detecting: YES (saat wajah di frame)

### Debug Logs:
- Detailed initialization logs
- Face detection success/failure logs
- Render state information
- Error details dengan stack trace

## Troubleshooting

### Common Issues:

1. **FPS rendah (<15)**:
   - Check CPU usage
   - Reduce video resolution
   - Disable unnecessary features

2. **Face detection tidak bekerja**:
   - Check camera permissions
   - Ensure good lighting
   - Check MediaPipe initialization

3. **Glasses tidak muncul**:
   - Check model loading
   - Verify Three.js initialization
   - Test manual positioning

4. **High error count**:
   - Check console for error details
   - Verify all dependencies loaded
   - Check network connectivity

## Integration dengan Existing System

Debug features terintegrasi dengan:
- ✅ Try-On Canvas component
- ✅ Face Detection system
- ✅ Three.js Glasses Renderer
- ✅ MediaPipe integration
- ✅ Store management (useTryOnStore)

## Performance Impact

Debug features dirancang dengan minimal performance impact:
- Debug logging hanya aktif saat debug mode ON
- FPS monitoring menggunakan efficient counters
- Overlay rendering minimal overhead
- Console methods hanya tersedia saat diperlukan

## Next Steps

Untuk pengembangan lebih lanjut:
1. Add network performance monitoring
2. Add memory usage tracking
3. Add model loading performance metrics
4. Add automated testing integration
5. Add debug data export functionality
