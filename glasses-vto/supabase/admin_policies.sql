-- =============================================
-- ADMIN RLS POLICIES FOR CRUD OPERATIONS
-- =============================================
-- This file adds missing RLS policies for authenticated users to perform
-- INSERT, UPDATE, DELETE operations on catalog tables

-- =============================================
-- PRODUCTS TABLE ADMIN POLICIES
-- =============================================

-- Allow authenticated users to insert products
CREATE POLICY "authenticated_insert_products" ON public.products
  FOR INSERT 
  WITH CHECK (auth.uid() IS NOT NULL);

-- Allow authenticated users to update products
CREATE POLICY "authenticated_update_products" ON public.products
  FOR UPDATE 
  USING (auth.uid() IS NOT NULL)
  WITH CHECK (auth.uid() IS NOT NULL);

-- Allow authenticated users to delete products
CREATE POLICY "authenticated_delete_products" ON public.products
  FOR DELETE 
  USING (auth.uid() IS NOT NULL);

-- =============================================
-- CATEGORIES TABLE ADMIN POLICIES
-- =============================================

-- Allow authenticated users to insert categories
CREATE POLICY "authenticated_insert_categories" ON public.categories
  FOR INSERT 
  WITH CHECK (auth.uid() IS NOT NULL);

-- Allow authenticated users to update categories
CREATE POLICY "authenticated_update_categories" ON public.categories
  FOR UPDATE 
  USING (auth.uid() IS NOT NULL)
  WITH CHECK (auth.uid() IS NOT NULL);

-- Allow authenticated users to delete categories
CREATE POLICY "authenticated_delete_categories" ON public.categories
  FOR DELETE 
  USING (auth.uid() IS NOT NULL);

-- =============================================
-- PRODUCT_CATEGORIES TABLE ADMIN POLICIES
-- =============================================

-- Allow authenticated users to insert product-category relationships
CREATE POLICY "authenticated_insert_product_categories" ON public.product_categories
  FOR INSERT 
  WITH CHECK (auth.uid() IS NOT NULL);

-- Allow authenticated users to update product-category relationships
CREATE POLICY "authenticated_update_product_categories" ON public.product_categories
  FOR UPDATE 
  USING (auth.uid() IS NOT NULL)
  WITH CHECK (auth.uid() IS NOT NULL);

-- Allow authenticated users to delete product-category relationships
CREATE POLICY "authenticated_delete_product_categories" ON public.product_categories
  FOR DELETE 
  USING (auth.uid() IS NOT NULL);

-- =============================================
-- PRODUCT_VARIANTS TABLE ADMIN POLICIES
-- =============================================

-- Allow authenticated users to insert product variants
CREATE POLICY "authenticated_insert_product_variants" ON public.product_variants
  FOR INSERT 
  WITH CHECK (auth.uid() IS NOT NULL);

-- Allow authenticated users to update product variants
CREATE POLICY "authenticated_update_product_variants" ON public.product_variants
  FOR UPDATE 
  USING (auth.uid() IS NOT NULL)
  WITH CHECK (auth.uid() IS NOT NULL);

-- Allow authenticated users to delete product variants
CREATE POLICY "authenticated_delete_product_variants" ON public.product_variants
  FOR DELETE 
  USING (auth.uid() IS NOT NULL);

-- =============================================
-- VARIANT_ASSETS TABLE ADMIN POLICIES
-- =============================================

-- Allow authenticated users to insert variant assets
CREATE POLICY "authenticated_insert_variant_assets" ON public.variant_assets
  FOR INSERT 
  WITH CHECK (auth.uid() IS NOT NULL);

-- Allow authenticated users to update variant assets
CREATE POLICY "authenticated_update_variant_assets" ON public.variant_assets
  FOR UPDATE 
  USING (auth.uid() IS NOT NULL)
  WITH CHECK (auth.uid() IS NOT NULL);

-- Allow authenticated users to delete variant assets
CREATE POLICY "authenticated_delete_variant_assets" ON public.variant_assets
  FOR DELETE 
  USING (auth.uid() IS NOT NULL);

-- =============================================
-- VERIFICATION QUERIES
-- =============================================
-- Run these to verify policies are working:

-- Check all policies for products table
-- SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check 
-- FROM pg_policies 
-- WHERE tablename = 'products';

-- Check all policies for categories table
-- SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check 
-- FROM pg_policies 
-- WHERE tablename = 'categories';
