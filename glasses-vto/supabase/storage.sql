-- =============================================
-- STORAGE BUCKETS SETUP
-- =============================================

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public)
VALUES 
  ('products', 'products', true),
  ('models', 'models', true),
  ('snapshots', 'snapshots', false);

-- =============================================
-- STORAGE POLICIES
-- =============================================

-- Products bucket: Public read, admin write
CREATE POLICY "Public Access" ON storage.objects 
  FOR SELECT 
  USING (bucket_id = 'products');

CREATE POLICY "Admin Insert" ON storage.objects 
  FOR INSERT 
  WITH CHECK (
    bucket_id = 'products' 
    AND auth.role() = 'service_role'
  );

CREATE POLICY "Admin Update" ON storage.objects 
  FOR UPDATE 
  USING (
    bucket_id = 'products' 
    AND auth.role() = 'service_role'
  );

CREATE POLICY "Admin Delete" ON storage.objects 
  FOR DELETE 
  USING (
    bucket_id = 'products' 
    AND auth.role() = 'service_role'
  );

-- Models bucket: Public read, admin write
CREATE POLICY "Public Access Models" ON storage.objects 
  FOR SELECT 
  USING (bucket_id = 'models');

CREATE POLICY "Admin Insert Models" ON storage.objects 
  FOR INSERT 
  WITH CHECK (
    bucket_id = 'models' 
    AND auth.role() = 'service_role'
  );

CREATE POLICY "Admin Update Models" ON storage.objects 
  FOR UPDATE 
  USING (
    bucket_id = 'models' 
    AND auth.role() = 'service_role'
  );

CREATE POLICY "Admin Delete Models" ON storage.objects 
  FOR DELETE 
  USING (
    bucket_id = 'models' 
    AND auth.role() = 'service_role'
  );

-- Snapshots bucket: Owner read/write
CREATE POLICY "User can view own snapshots" ON storage.objects 
  FOR SELECT 
  USING (
    bucket_id = 'snapshots' 
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

CREATE POLICY "User can upload own snapshots" ON storage.objects 
  FOR INSERT 
  WITH CHECK (
    bucket_id = 'snapshots' 
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

CREATE POLICY "User can update own snapshots" ON storage.objects 
  FOR UPDATE 
  USING (
    bucket_id = 'snapshots' 
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

CREATE POLICY "User can delete own snapshots" ON storage.objects 
  FOR DELETE 
  USING (
    bucket_id = 'snapshots' 
    AND (storage.foldername(name))[1] = auth.uid()::text
  );