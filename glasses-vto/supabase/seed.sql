-- =============================================
-- SEED DATA FOR GLASS VTO
-- =============================================

-- Insert Categories
INSERT INTO public.categories (slug, name) VALUES
  ('sunglasses', 'Sunglasses'),
  ('eyeglasses', 'Eyeglasses'),
  ('sport', 'Sport'),
  ('luxury', 'Luxury'),
  ('aviator', 'Aviator'),
  ('round', 'Round'),
  ('square', 'Square'),
  ('cat-eye', 'Cat Eye');

-- Insert Sample Products
INSERT INTO public.products (slug, name, description, brand, price_cents) VALUES
  ('aviator-classic', 'Aviator Classic', 'Timeless aviator design with UV protection', 'RayBan', 15900),
  ('wayfarer-original', 'Wayfarer Original', 'Iconic wayfarer style, perfect for any occasion', 'RayBan', 14900),
  ('round-metal', 'Round Metal', 'Vintage-inspired round metal frames', 'RayBan', 16900),
  ('clubmaster', 'Clubmaster', 'Retro browline style frames', 'RayBan', 15400),
  ('sport-pro', 'Sport Pro', 'Lightweight sport sunglasses for athletes', 'Oakley', 18900),
  ('cat-eye-vintage', 'Cat Eye Vintage', 'Elegant cat eye frames for a sophisticated look', 'Versace', 29900),
  ('square-modern', 'Square Modern', 'Contemporary square frames with bold design', 'Prada', 35900),
  ('round-classic', 'Round Classic', 'Classic round eyeglasses for everyday wear', 'Oliver Peoples', 42900);

-- Insert Product Variants for aviator-classic and wayfarer-original
INSERT INTO public.product_variants (
  product_id, 
  sku, 
  color, 
  size, 
  bridge_width_mm, 
  temple_length_mm, 
  lens_width_mm, 
  stock, 
  glb_path, 
  preview_image_path
) 
SELECT 
  p.id,
  CONCAT(p.slug, '-', color_variant.color, '-', size_variant.size),
  color_variant.color,
  size_variant.size,
  size_variant.bridge,
  size_variant.temple,
  size_variant.lens,
  20,
  CONCAT('models/', p.slug, '/', color_variant.color, '/model.glb'),
  CONCAT('products/', p.slug, '/', color_variant.color, '/preview.jpg')
FROM public.products p
CROSS JOIN (
  VALUES 
    ('black', 'black'),
    ('tortoise', 'tortoise'),
    ('gold', 'gold')
) AS color_variant(color, color_code)
CROSS JOIN (
  VALUES 
    ('small', 'S', 16, 135, 48),
    ('medium', 'M', 18, 140, 52),
    ('large', 'L', 20, 145, 55)
) AS size_variant(size, size_code, bridge, temple, lens)
WHERE p.slug IN ('aviator-classic', 'wayfarer-original');

-- Insert simplified variants for other products
INSERT INTO public.product_variants (
  product_id, 
  sku, 
  color, 
  size, 
  bridge_width_mm, 
  temple_length_mm, 
  lens_width_mm, 
  stock, 
  glb_path, 
  preview_image_path
) 
SELECT 
  p.id,
  CONCAT(p.slug, '-black-M'),
  'black',
  'medium',
  18,
  140,
  52,
  15,
  CONCAT('models/', p.slug, '/black/model.glb'),
  CONCAT('products/', p.slug, '/black/preview.jpg')
FROM public.products p
WHERE p.slug NOT IN ('aviator-classic', 'wayfarer-original');

-- Link Products to Categories
INSERT INTO public.product_categories (product_id, category_id)
SELECT p.id, c.id
FROM public.products p, public.categories c
WHERE 
  (p.slug = 'aviator-classic' AND c.slug IN ('sunglasses', 'aviator', 'luxury')) OR
  (p.slug = 'wayfarer-original' AND c.slug IN ('sunglasses', 'square')) OR
  (p.slug = 'round-metal' AND c.slug IN ('sunglasses', 'round', 'luxury')) OR
  (p.slug = 'clubmaster' AND c.slug IN ('sunglasses', 'square')) OR
  (p.slug = 'sport-pro' AND c.slug IN ('sunglasses', 'sport')) OR
  (p.slug = 'cat-eye-vintage' AND c.slug IN ('sunglasses', 'cat-eye', 'luxury')) OR
  (p.slug = 'square-modern' AND c.slug IN ('sunglasses', 'square', 'luxury')) OR
  (p.slug = 'round-classic' AND c.slug IN ('eyeglasses', 'round'));

-- Insert sample variant assets for the main products
INSERT INTO public.variant_assets (variant_id, kind, path, meta)
SELECT 
  pv.id,
  'thumbnail',
  CONCAT('products/', p.slug, '/', pv.color, '/thumb.jpg'),
  '{"width": 300, "height": 300}'::jsonb
FROM public.product_variants pv
JOIN public.products p ON p.id = pv.product_id
WHERE p.slug IN ('aviator-classic', 'wayfarer-original');