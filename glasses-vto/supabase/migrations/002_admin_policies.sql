-- =============================================
-- ADMIN RLS POLICIES FOR CRUD OPERATIONS
-- Migration: 002_admin_policies.sql
-- =============================================

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "authenticated_insert_products" ON public.products;
DROP POLICY IF EXISTS "authenticated_update_products" ON public.products;
DROP POLICY IF EXISTS "authenticated_delete_products" ON public.products;

DROP POLICY IF EXISTS "authenticated_insert_categories" ON public.categories;
DROP POLICY IF EXISTS "authenticated_update_categories" ON public.categories;
DROP POLICY IF EXISTS "authenticated_delete_categories" ON public.categories;

DROP POLICY IF EXISTS "authenticated_insert_product_categories" ON public.product_categories;
DROP POLICY IF EXISTS "authenticated_update_product_categories" ON public.product_categories;
DROP POLICY IF EXISTS "authenticated_delete_product_categories" ON public.product_categories;

DROP POLICY IF EXISTS "authenticated_insert_product_variants" ON public.product_variants;
DROP POLICY IF EXISTS "authenticated_update_product_variants" ON public.product_variants;
DROP POLICY IF EXISTS "authenticated_delete_product_variants" ON public.product_variants;

DROP POLICY IF EXISTS "authenticated_insert_variant_assets" ON public.variant_assets;
DROP POLICY IF EXISTS "authenticated_update_variant_assets" ON public.variant_assets;
DROP POLICY IF EXISTS "authenticated_delete_variant_assets" ON public.variant_assets;

-- =============================================
-- PRODUCTS TABLE ADMIN POLICIES
-- =============================================

CREATE POLICY "authenticated_insert_products" ON public.products
  FOR INSERT 
  WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "authenticated_update_products" ON public.products
  FOR UPDATE 
  USING (auth.uid() IS NOT NULL)
  WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "authenticated_delete_products" ON public.products
  FOR DELETE 
  USING (auth.uid() IS NOT NULL);

-- =============================================
-- CATEGORIES TABLE ADMIN POLICIES
-- =============================================

CREATE POLICY "authenticated_insert_categories" ON public.categories
  FOR INSERT 
  WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "authenticated_update_categories" ON public.categories
  FOR UPDATE 
  USING (auth.uid() IS NOT NULL)
  WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "authenticated_delete_categories" ON public.categories
  FOR DELETE 
  USING (auth.uid() IS NOT NULL);

-- =============================================
-- PRODUCT_CATEGORIES TABLE ADMIN POLICIES
-- =============================================

CREATE POLICY "authenticated_insert_product_categories" ON public.product_categories
  FOR INSERT 
  WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "authenticated_update_product_categories" ON public.product_categories
  FOR UPDATE 
  USING (auth.uid() IS NOT NULL)
  WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "authenticated_delete_product_categories" ON public.product_categories
  FOR DELETE 
  USING (auth.uid() IS NOT NULL);

-- =============================================
-- PRODUCT_VARIANTS TABLE ADMIN POLICIES
-- =============================================

CREATE POLICY "authenticated_insert_product_variants" ON public.product_variants
  FOR INSERT 
  WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "authenticated_update_product_variants" ON public.product_variants
  FOR UPDATE 
  USING (auth.uid() IS NOT NULL)
  WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "authenticated_delete_product_variants" ON public.product_variants
  FOR DELETE 
  USING (auth.uid() IS NOT NULL);

-- =============================================
-- VARIANT_ASSETS TABLE ADMIN POLICIES
-- =============================================

CREATE POLICY "authenticated_insert_variant_assets" ON public.variant_assets
  FOR INSERT 
  WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "authenticated_update_variant_assets" ON public.variant_assets
  FOR UPDATE 
  USING (auth.uid() IS NOT NULL)
  WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "authenticated_delete_variant_assets" ON public.variant_assets
  FOR DELETE 
  USING (auth.uid() IS NOT NULL);
