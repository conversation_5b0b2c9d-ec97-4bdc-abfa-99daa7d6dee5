-- =============================================
-- GLASS VTO DATABASE SCHEMA
-- =============================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- TABLES
-- =============================================

-- PRODUCTS TABLE
CREATE TABLE public.products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  slug TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  brand TEXT,
  price_cents INT NOT NULL CHECK (price_cents >= 0),
  active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE INDEX idx_products_active ON public.products (active);
CREATE INDEX idx_products_slug ON public.products (slug);
CREATE INDEX idx_products_created_at ON public.products (created_at DESC);

-- PRODUCT VARIANTS TABLE (colors/sizes/lens options)
CREATE TABLE public.product_variants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
  sku TEXT UNIQUE,
  color TEXT,
  size TEXT,
  bridge_width_mm INT,
  temple_length_mm INT,
  lens_width_mm INT,
  stock INT DEFAULT 0,
  glb_path TEXT,            -- storage path (models bucket)
  preview_image_path TEXT,  -- storage path (products bucket)
  active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE INDEX idx_product_variants_product_id ON public.product_variants (product_id);
CREATE INDEX idx_product_variants_active ON public.product_variants (active);

-- VARIANT ASSETS TABLE (additional textures/materials)
CREATE TABLE public.variant_assets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  variant_id UUID NOT NULL REFERENCES public.product_variants(id) ON DELETE CASCADE,
  kind TEXT NOT NULL CHECK (kind IN ('texture','thumbnail','ar-mask','extra')),
  path TEXT NOT NULL,   -- storage path
  meta JSONB DEFAULT '{}'::jsonb
);

CREATE INDEX idx_variant_assets_variant_id ON public.variant_assets (variant_id);

-- CATEGORIES TABLE
CREATE TABLE public.categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  slug TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL
);

CREATE INDEX idx_categories_slug ON public.categories (slug);

-- PRODUCT CATEGORIES (many-to-many)
CREATE TABLE public.product_categories (
  product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
  category_id UUID REFERENCES public.categories(id) ON DELETE CASCADE,
  PRIMARY KEY (product_id, category_id)
);

-- WISHLIST TABLE
CREATE TABLE public.wishlists (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE (user_id, product_id)
);

CREATE INDEX idx_wishlists_user_id ON public.wishlists (user_id);

-- TRY-ON EVENTS TABLE (telemetry)
CREATE TABLE public.tryon_events (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  product_id UUID REFERENCES public.products(id) ON DELETE SET NULL,
  variant_id UUID REFERENCES public.product_variants(id) ON DELETE SET NULL,
  device TEXT,
  fps NUMERIC,
  width INT,
  height INT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE INDEX idx_tryon_events_created_at ON public.tryon_events (created_at DESC);
CREATE INDEX idx_tryon_events_user_id ON public.tryon_events (user_id);

-- =============================================
-- ROW LEVEL SECURITY (RLS)
-- =============================================

-- Enable RLS on all tables
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.variant_assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.wishlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tryon_events ENABLE ROW LEVEL SECURITY;

-- Public read-only for catalog tables
CREATE POLICY "public_read_products" ON public.products
  FOR SELECT USING (true);

CREATE POLICY "public_read_variants" ON public.product_variants
  FOR SELECT USING (true);

CREATE POLICY "public_read_variant_assets" ON public.variant_assets
  FOR SELECT USING (true);

CREATE POLICY "public_read_categories" ON public.categories
  FOR SELECT USING (true);

CREATE POLICY "public_read_product_categories" ON public.product_categories
  FOR SELECT USING (true);

-- Wishlist: only owner can read/write
CREATE POLICY "wishlist_select_own" ON public.wishlists
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "wishlist_insert_own" ON public.wishlists
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "wishlist_delete_own" ON public.wishlists
  FOR DELETE USING (auth.uid() = user_id);

-- Try-on events: insert by logged-in or anonymous
CREATE POLICY "tryon_insert_own_or_anon" ON public.tryon_events
  FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "tryon_select_own" ON public.tryon_events
  FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

-- =============================================
-- SQL FUNCTIONS (RPC)
-- =============================================

-- Toggle wishlist atomically
CREATE OR REPLACE FUNCTION public.toggle_wishlist(p_product_id UUID)
RETURNS VOID 
LANGUAGE plpgsql 
SECURITY DEFINER
AS $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM public.wishlists
    WHERE user_id = auth.uid() AND product_id = p_product_id
  ) THEN
    DELETE FROM public.wishlists
    WHERE user_id = auth.uid() AND product_id = p_product_id;
  ELSE
    INSERT INTO public.wishlists (user_id, product_id)
    VALUES (auth.uid(), p_product_id);
  END IF;
END;
$$;

-- Get catalog with filters
CREATE OR REPLACE FUNCTION public.get_catalog(
  p_search TEXT DEFAULT NULL,
  p_category_slug TEXT DEFAULT NULL,
  p_color TEXT DEFAULT NULL,
  p_price_min INT DEFAULT NULL,
  p_price_max INT DEFAULT NULL,
  p_limit INT DEFAULT 24,
  p_offset INT DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  slug TEXT,
  name TEXT,
  description TEXT,
  brand TEXT,
  price_cents INT,
  active BOOLEAN,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
)
LANGUAGE sql
STABLE
AS $$
  SELECT 
    p.id,
    p.slug,
    p.name,
    p.description,
    p.brand,
    p.price_cents,
    p.active,
    p.created_at,
    p.updated_at
  FROM public.products p
  WHERE p.active = true
    AND (p_search IS NULL OR p.name ILIKE '%'||p_search||'%' OR p.description ILIKE '%'||p_search||'%')
    AND (p_category_slug IS NULL OR EXISTS (
      SELECT 1 FROM public.product_categories pc
      JOIN public.categories c ON c.id = pc.category_id
      WHERE pc.product_id = p.id AND c.slug = p_category_slug
    ))
    AND (p_color IS NULL OR EXISTS (
      SELECT 1 FROM public.product_variants pv
      WHERE pv.product_id = p.id AND pv.color ILIKE '%'||p_color||'%'
    ))
    AND (p_price_min IS NULL OR p.price_cents >= p_price_min)
    AND (p_price_max IS NULL OR p.price_cents <= p_price_max)
  ORDER BY p.created_at DESC
  LIMIT p_limit OFFSET p_offset;
$$;

-- Get product with variants
CREATE OR REPLACE FUNCTION public.get_product_details(p_slug TEXT)
RETURNS TABLE (
  product_id UUID,
  product_name TEXT,
  product_description TEXT,
  product_brand TEXT,
  product_price_cents INT,
  variant_id UUID,
  variant_sku TEXT,
  variant_color TEXT,
  variant_size TEXT,
  variant_glb_path TEXT,
  variant_preview_image_path TEXT,
  variant_stock INT,
  categories TEXT[]
)
LANGUAGE sql
STABLE
AS $$
  SELECT 
    p.id AS product_id,
    p.name AS product_name,
    p.description AS product_description,
    p.brand AS product_brand,
    p.price_cents AS product_price_cents,
    pv.id AS variant_id,
    pv.sku AS variant_sku,
    pv.color AS variant_color,
    pv.size AS variant_size,
    pv.glb_path AS variant_glb_path,
    pv.preview_image_path AS variant_preview_image_path,
    pv.stock AS variant_stock,
    ARRAY(
      SELECT c.name 
      FROM public.categories c 
      JOIN public.product_categories pc ON pc.category_id = c.id 
      WHERE pc.product_id = p.id
    ) AS categories
  FROM public.products p
  LEFT JOIN public.product_variants pv ON pv.product_id = p.id AND pv.active = true
  WHERE p.slug = p_slug AND p.active = true;
$$;

-- Get user wishlist with product details
CREATE OR REPLACE FUNCTION public.get_user_wishlist()
RETURNS TABLE (
  wishlist_id UUID,
  product_id UUID,
  product_name TEXT,
  product_brand TEXT,
  product_price_cents INT,
  product_slug TEXT,
  preview_image_path TEXT,
  added_at TIMESTAMPTZ
)
LANGUAGE sql
STABLE
AS $$
  SELECT 
    w.id AS wishlist_id,
    p.id AS product_id,
    p.name AS product_name,
    p.brand AS product_brand,
    p.price_cents AS product_price_cents,
    p.slug AS product_slug,
    (SELECT pv.preview_image_path 
     FROM public.product_variants pv 
     WHERE pv.product_id = p.id 
     LIMIT 1) AS preview_image_path,
    w.created_at AS added_at
  FROM public.wishlists w
  JOIN public.products p ON p.id = w.product_id
  WHERE w.user_id = auth.uid()
  ORDER BY w.created_at DESC;
$$;

-- Update timestamps trigger
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER products_updated_at
  BEFORE UPDATE ON public.products
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();