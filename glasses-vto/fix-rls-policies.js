const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase credentials in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testWithAuth() {
  try {
    console.log('🔐 Testing with authenticated user...')
    
    // Sign in with admin user
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    })
    
    if (authError) {
      console.error('❌ Auth error:', authError.message)
      return
    }
    
    console.log('✅ Signed in as:', authData.user.email)
    
    // Test creating a product
    console.log('\n📦 Testing product creation...')
    const { data: product, error: productError } = await supabase
      .from('products')
      .insert({
        slug: 'test-glasses-' + Date.now(),
        name: 'Test Glasses',
        description: 'Test glasses for admin panel',
        brand: 'Test Brand',
        price_cents: 25000
      })
      .select()
      .single()
    
    if (productError) {
      console.error('❌ Product creation failed:', productError.message)
      console.log('This confirms we need to add RLS policies for authenticated users')
    } else {
      console.log('✅ Product created successfully:', product.name)
      
      // Test updating the product
      console.log('\n✏️ Testing product update...')
      const { data: updatedProduct, error: updateError } = await supabase
        .from('products')
        .update({ name: 'Updated Test Glasses' })
        .eq('id', product.id)
        .select()
        .single()
      
      if (updateError) {
        console.error('❌ Product update failed:', updateError.message)
      } else {
        console.log('✅ Product updated successfully:', updatedProduct.name)
      }
      
      // Test deleting the product
      console.log('\n🗑️ Testing product deletion...')
      const { error: deleteError } = await supabase
        .from('products')
        .delete()
        .eq('id', product.id)
      
      if (deleteError) {
        console.error('❌ Product deletion failed:', deleteError.message)
      } else {
        console.log('✅ Product deleted successfully')
      }
    }
    
    // Test creating a category
    console.log('\n🏷️ Testing category creation...')
    const { data: category, error: categoryError } = await supabase
      .from('categories')
      .insert({
        slug: 'test-category-' + Date.now(),
        name: 'Test Category'
      })
      .select()
      .single()
    
    if (categoryError) {
      console.error('❌ Category creation failed:', categoryError.message)
    } else {
      console.log('✅ Category created successfully:', category.name)
      
      // Clean up category
      await supabase.from('categories').delete().eq('id', category.id)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

async function addRLSPoliciesViaSQL() {
  try {
    console.log('🔧 Attempting to add RLS policies via SQL...')
    
    // Sign in as admin first
    const { error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    })
    
    if (authError) {
      console.error('❌ Auth error:', authError.message)
      return
    }
    
    // Try to execute SQL directly (this might not work with anon key)
    const policies = [
      `CREATE POLICY IF NOT EXISTS "authenticated_insert_products" ON public.products FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);`,
      `CREATE POLICY IF NOT EXISTS "authenticated_update_products" ON public.products FOR UPDATE USING (auth.uid() IS NOT NULL) WITH CHECK (auth.uid() IS NOT NULL);`,
      `CREATE POLICY IF NOT EXISTS "authenticated_delete_products" ON public.products FOR DELETE USING (auth.uid() IS NOT NULL);`,
      `CREATE POLICY IF NOT EXISTS "authenticated_insert_categories" ON public.categories FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);`,
      `CREATE POLICY IF NOT EXISTS "authenticated_update_categories" ON public.categories FOR UPDATE USING (auth.uid() IS NOT NULL) WITH CHECK (auth.uid() IS NOT NULL);`,
      `CREATE POLICY IF NOT EXISTS "authenticated_delete_categories" ON public.categories FOR DELETE USING (auth.uid() IS NOT NULL);`
    ]
    
    for (const policy of policies) {
      console.log('Executing:', policy.substring(0, 50) + '...')
      // This will likely fail without service role key
      // But let's try anyway
    }
    
    console.log('⚠️ SQL execution requires service role key or database admin access')
    console.log('Please run the migration manually in Supabase dashboard')
    
  } catch (error) {
    console.error('❌ SQL execution failed:', error.message)
  }
}

// Run the test
if (require.main === module) {
  console.log('🚀 Starting RLS policy fix and test...')
  
  testWithAuth()
    .then(() => {
      console.log('\n📋 Summary:')
      console.log('- If product/category creation failed, RLS policies need to be added')
      console.log('- Please run the SQL migration in Supabase dashboard:')
      console.log('  supabase/migrations/002_admin_policies.sql')
      console.log('- Or manually add policies for authenticated users to INSERT/UPDATE/DELETE')
    })
    .then(() => process.exit(0))
    .catch(() => process.exit(1))
}

module.exports = { testWithAuth, addRLSPoliciesViaSQL }
