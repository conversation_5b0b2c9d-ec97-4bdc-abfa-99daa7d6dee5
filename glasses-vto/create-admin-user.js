const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase credentials in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function createAdminUser() {
  try {
    console.log('🔧 Creating admin user...')
    
    const { data, error } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'admin123',
      options: {
        data: {
          role: 'admin'
        },
        emailRedirectTo: undefined
      }
    })
    
    if (error) {
      if (error.message.includes('already registered')) {
        console.log('✅ Admin user already exists')
        
        // Try to sign in to verify
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: '<EMAIL>',
          password: 'admin123'
        })
        
        if (signInError) {
          console.error('❌ Cannot sign in with admin credentials:', signInError.message)
        } else {
          console.log('✅ Admin user can sign in successfully')
          console.log('User ID:', signInData.user?.id)
        }
      } else {
        console.error('❌ Error creating admin user:', error.message)
      }
    } else {
      console.log('✅ Admin user created successfully!')
      console.log('User ID:', data.user?.id)
      console.log('Email:', data.user?.email)
    }
    
  } catch (error) {
    console.error('❌ Failed to create admin user:', error.message)
  }
}

// Test database connection first
async function testConnection() {
  try {
    console.log('🧪 Testing database connection...')
    
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .limit(1)
    
    if (error) {
      console.error('❌ Database connection failed:', error.message)
      return false
    } else {
      console.log('✅ Database connected successfully')
      return true
    }
  } catch (error) {
    console.error('❌ Database connection error:', error.message)
    return false
  }
}

// Run the script
if (require.main === module) {
  testConnection()
    .then(connected => {
      if (connected) {
        return createAdminUser()
      } else {
        console.error('❌ Cannot proceed without database connection')
        process.exit(1)
      }
    })
    .then(() => process.exit(0))
    .catch(() => process.exit(1))
}

module.exports = { createAdminUser, testConnection }
