# Admin Panel Guide - Glass VTO

## 🔐 Accessing Admin Panel

### URL
```
http://localhost:3000/admin
```

### Authentication
1. First, create an admin user in Supabase:
   - Go to Supabase Dashboard → Authentication → Users
   - Click "Invite User" or "Create User"
   - Enter email and password
   - Example: `<EMAIL>` / `admin123`

2. Login at: http://localhost:3000/auth/login

## 📊 Admin Features

### Dashboard
- **URL**: `/admin`
- **Features**:
  - Overview statistics
  - Quick actions
  - Recent activity

### Products Management
- **URL**: `/admin/products`
- **Features**:
  - List all products
  - Search and filter
  - Add new products
  - Edit existing products
  - Delete products
  - Toggle active/inactive status
  - Manage product variants

### Categories (Coming Soon)
- **URL**: `/admin/categories`
- **Features**:
  - Add/Edit/Delete categories
  - Organize product taxonomy

### Media Management (Coming Soon)
- **URL**: `/admin/media`
- **Features**:
  - Upload product images
  - Upload 3D models (.glb files)
  - Manage storage

## 🛠️ Admin Operations

### Adding a New Product

1. Navigate to `/admin/products`
2. Click "Add Product" button
3. Fill in the form:
   - **Product Name**: Display name
   - **URL Slug**: URL-friendly identifier
   - **Brand**: Manufacturer name
   - **Price**: In USD
   - **Description**: Product details
   - **Categories**: Select applicable categories
   - **Status**: Active/Inactive toggle
4. Click "Create Product"
5. You'll be redirected to add variants

### Managing Product Variants

After creating a product:
1. Add color variants (black, tortoise, gold, etc.)
2. Add size variants (S, M, L)
3. Upload preview images for each variant
4. Upload 3D models (.glb files)

### Uploading Media Files

#### Product Images
- **Format**: JPG/PNG
- **Recommended Size**: 800x600px
- **Path**: `products/{slug}/{color}/preview.jpg`

#### 3D Models
- **Format**: GLB (GLTF Binary)
- **Max Size**: 5MB recommended
- **Path**: `models/{slug}/{color}/model.glb`

## 📁 File Structure

```
admin/
├── layout.tsx          # Admin layout with sidebar
├── page.tsx           # Dashboard
├── products/
│   ├── page.tsx       # Products list
│   ├── new/
│   │   └── page.tsx   # Create product
│   └── [id]/
│       ├── edit/      # Edit product
│       └── variants/  # Manage variants
├── categories/        # Categories CRUD
└── media/            # Media management
```

## 🔌 API Endpoints

### Admin APIs (Protected)

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/admin/stats` | Dashboard statistics |
| GET | `/api/admin/products` | List all products |
| POST | `/api/admin/products` | Create product |
| PATCH | `/api/admin/products/[id]` | Update product |
| DELETE | `/api/admin/products/[id]` | Delete product |

## 🚀 Next Steps

### To Complete the Admin Panel:

1. **Categories Management**
   ```typescript
   // Create /admin/categories/page.tsx
   // Add CRUD operations for categories
   ```

2. **Product Variants Manager**
   ```typescript
   // Create /admin/products/[id]/variants/page.tsx
   // Add interface for managing sizes/colors
   ```

3. **Media Upload Interface**
   ```typescript
   // Create /admin/media/page.tsx
   // Integrate with Supabase Storage
   ```

4. **User Management**
   ```typescript
   // Create /admin/users/page.tsx
   // View users and wishlist data
   ```

## 🔒 Security Considerations

1. **Authentication Required**: All admin routes check for authenticated user
2. **Role-Based Access**: Consider adding admin role check
3. **Service Role Key**: Never expose in client-side code
4. **Input Validation**: Validate all form inputs
5. **SQL Injection**: Use parameterized queries (Supabase handles this)

## 📝 Common Tasks

### Add Admin Role Check

```typescript
// In admin routes, add role verification:
const { data: profile } = await supabase
  .from('profiles')
  .select('role')
  .eq('id', user.id)
  .single()

if (profile?.role !== 'admin') {
  return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
}
```

### Bulk Operations

```sql
-- Bulk update products status
UPDATE products SET active = false WHERE created_at < '2024-01-01';

-- Bulk delete old try-on events
DELETE FROM tryon_events WHERE created_at < NOW() - INTERVAL '30 days';
```

## 🐛 Troubleshooting

### "Unauthorized" Error
- Check if user is logged in
- Verify Supabase session is valid
- Check browser cookies

### Products Not Loading
- Verify database connection
- Check if seed data exists
- Review browser console for errors

### Images Not Displaying
- Check Storage bucket permissions
- Verify file paths are correct
- Ensure buckets are public (for products/models)

## 📧 Support

For issues or questions:
1. Check Supabase logs
2. Review browser DevTools console
3. Check API responses in Network tab
4. Refer to `/brief project/backend.md`

---

**Note**: This admin panel is a basic implementation. For production use, consider adding:
- Advanced filtering and sorting
- Bulk operations
- Export/Import functionality  
- Analytics and reporting
- Audit logs
- Two-factor authentication