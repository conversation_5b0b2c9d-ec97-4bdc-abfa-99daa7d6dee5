# Backend Setup Guide - Glass VTO Application

## Prerequisites
- Supabase account (https://supabase.com)
- Node.js 18+ installed
- npm or yarn package manager

## Setup Instructions

### 1. Create Supabase Project

1. Go to [Supabase Dashboard](https://app.supabase.com)
2. Create a new project
3. Save your project credentials:
   - Project URL
   - Anon/Public Key
   - Service Role Key (for admin operations)

### 2. Configure Environment Variables

Update `.env.local` with your Supabase credentials:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_project_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here

# Optional: For admin operations only (keep secure!)
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### 3. Setup Database Schema

1. Go to your Supabase Dashboard
2. Navigate to **SQL Editor**
3. Run the following SQL scripts in order:

#### Step 1: Initial Schema
```sql
-- Copy and paste content from:
-- supabase/migrations/20240118000000_initial_schema.sql
```

#### Step 2: Storage Buckets
```sql
-- Copy and paste content from:
-- supabase/storage.sql
```

#### Step 3: Seed Data (Optional)
```sql
-- Copy and paste content from:
-- supabase/seed.sql
```

### 4. Configure Storage Buckets

After running the storage SQL:

1. Go to **Storage** in Supabase Dashboard
2. Verify these buckets are created:
   - `products` - Public (for product images)
   - `models` - Public (for 3D GLB files)
   - `snapshots` - Private (for user try-on photos)

3. Upload sample files (optional):
   - Product images: `products/{product-slug}/{color}/preview.jpg`
   - 3D Models: `models/{product-slug}/{color}/model.glb`

### 5. Enable Authentication

1. Go to **Authentication** → **Providers**
2. Enable **Email** authentication
3. Optional: Enable social providers (Google, Apple)
4. Configure authentication settings:
   - Enable email confirmations (optional)
   - Set redirect URLs for your domain

### 6. Test the Backend

Run the development server:

```bash
npm run dev
# or
yarn dev
```

Test API endpoints:

```javascript
// Test in browser console or API client

// Get products catalog
const response = await fetch('/api/products')
const products = await response.json()

// Get categories
const categories = await fetch('/api/categories')

// Toggle wishlist (requires authentication)
const wishlistResponse = await fetch('/api/wishlist', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ productId: 'product-uuid' })
})
```

## API Endpoints Overview

### Public Endpoints (No Auth Required)

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/products` | Get products catalog with filters |
| GET | `/api/products/[slug]` | Get product details |
| GET | `/api/categories` | Get all categories |
| GET | `/api/search` | Search products |

### Protected Endpoints (Auth Required)

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/wishlist` | Get user wishlist |
| POST | `/api/wishlist` | Toggle product in wishlist |
| DELETE | `/api/wishlist/[id]` | Remove from wishlist |
| POST | `/api/snapshots` | Upload try-on snapshot |
| GET | `/api/snapshots` | Get user snapshots |

## Database Structure

### Main Tables
- `products` - Product catalog
- `product_variants` - Color/size variations
- `categories` - Product categories
- `wishlists` - User wishlists
- `tryon_events` - Analytics/telemetry

### SQL Functions (RPC)
- `get_catalog()` - Filtered product search
- `get_product_details()` - Product with variants
- `toggle_wishlist()` - Add/remove wishlist items
- `get_user_wishlist()` - User's wishlist with details

## Storage Structure

```
storage/
├── products/       # Public - Product images
│   └── {slug}/
│       └── {color}/
│           ├── preview.jpg
│           └── thumb.jpg
├── models/         # Public - 3D GLB files  
│   └── {slug}/
│       └── {color}/
│           └── model.glb
└── snapshots/      # Private - User photos
    └── {user-id}/
        └── {timestamp}.jpg
```

## Security Features

### Row Level Security (RLS)
- ✅ All tables have RLS enabled
- ✅ Public read for catalog data
- ✅ User-scoped write for personal data
- ✅ Admin operations via service role

### Storage Policies
- ✅ Public read for products/models
- ✅ Owner-only access for snapshots
- ✅ Admin-only write for products/models

## Monitoring & Maintenance

### Check Database Performance
1. Go to **Database** → **Query Performance**
2. Monitor slow queries
3. Add indexes if needed

### Storage Management
1. Monitor storage usage in dashboard
2. Set up lifecycle policies for old snapshots
3. Optimize image sizes before upload

## Troubleshooting

### Common Issues

1. **"Permission denied" errors**
   - Check RLS policies
   - Verify user authentication
   - Check API keys in .env.local

2. **Storage upload fails**
   - Check bucket policies
   - Verify file size limits
   - Check CORS settings

3. **Slow queries**
   - Add appropriate indexes
   - Use the RPC functions instead of joins
   - Enable query caching

### Debug Mode

Enable Supabase debug logging:

```javascript
// In your API routes
const supabase = createClient()
supabase.auth.debug = true // Enable auth debugging
```

## Production Checklist

- [ ] Enable email confirmation
- [ ] Set up custom domain
- [ ] Configure CORS for your domain
- [ ] Set up database backups
- [ ] Enable query performance monitoring
- [ ] Review and tighten RLS policies
- [ ] Set up error tracking (Sentry)
- [ ] Configure rate limiting
- [ ] Set up CDN for storage

## Support & Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Database Schema](/supabase/migrations/)
- [API Helper Functions](/src/lib/api/)
- Project Brief: `/brief project/backend.md`

## Next Steps

1. Test all API endpoints
2. Upload sample 3D models and images
3. Configure authentication providers
4. Set up monitoring and alerts
5. Optimize database queries

---

For questions or issues, refer to the project documentation in `/brief project/` directory.