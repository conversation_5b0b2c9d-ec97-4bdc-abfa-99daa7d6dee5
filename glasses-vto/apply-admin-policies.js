const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials in .env.local')
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function applyAdminPolicies() {
  try {
    console.log('🔧 Applying admin RLS policies...')
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, 'supabase', 'admin_policies.sql')
    const sqlContent = fs.readFileSync(sqlPath, 'utf8')
    
    // Split by semicolon and filter out comments and empty lines
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt && !stmt.startsWith('--') && !stmt.startsWith('/*'))
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`)
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      if (statement) {
        console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`)
        
        const { error } = await supabase.rpc('exec_sql', { sql: statement })
        
        if (error) {
          console.error(`❌ Error executing statement ${i + 1}:`, error.message)
          console.error('Statement:', statement.substring(0, 100) + '...')
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`)
        }
      }
    }
    
    console.log('🎉 Admin policies applied successfully!')
    
    // Test the policies by trying to fetch products
    console.log('🧪 Testing policies...')
    const { data: products, error: testError } = await supabase
      .from('products')
      .select('*')
      .limit(1)
    
    if (testError) {
      console.error('❌ Test failed:', testError.message)
    } else {
      console.log('✅ Policies test passed - can read products')
    }
    
  } catch (error) {
    console.error('❌ Failed to apply admin policies:', error.message)
    process.exit(1)
  }
}

// Alternative method using direct SQL execution
async function applyPoliciesDirectly() {
  try {
    console.log('🔧 Applying admin RLS policies directly...')
    
    const policies = [
      // Products policies
      `CREATE POLICY "authenticated_insert_products" ON public.products FOR INSERT WITH CHECK (auth.uid() IS NOT NULL)`,
      `CREATE POLICY "authenticated_update_products" ON public.products FOR UPDATE USING (auth.uid() IS NOT NULL) WITH CHECK (auth.uid() IS NOT NULL)`,
      `CREATE POLICY "authenticated_delete_products" ON public.products FOR DELETE USING (auth.uid() IS NOT NULL)`,
      
      // Categories policies
      `CREATE POLICY "authenticated_insert_categories" ON public.categories FOR INSERT WITH CHECK (auth.uid() IS NOT NULL)`,
      `CREATE POLICY "authenticated_update_categories" ON public.categories FOR UPDATE USING (auth.uid() IS NOT NULL) WITH CHECK (auth.uid() IS NOT NULL)`,
      `CREATE POLICY "authenticated_delete_categories" ON public.categories FOR DELETE USING (auth.uid() IS NOT NULL)`,
      
      // Product categories policies
      `CREATE POLICY "authenticated_insert_product_categories" ON public.product_categories FOR INSERT WITH CHECK (auth.uid() IS NOT NULL)`,
      `CREATE POLICY "authenticated_update_product_categories" ON public.product_categories FOR UPDATE USING (auth.uid() IS NOT NULL) WITH CHECK (auth.uid() IS NOT NULL)`,
      `CREATE POLICY "authenticated_delete_product_categories" ON public.product_categories FOR DELETE USING (auth.uid() IS NOT NULL)`,
      
      // Product variants policies
      `CREATE POLICY "authenticated_insert_product_variants" ON public.product_variants FOR INSERT WITH CHECK (auth.uid() IS NOT NULL)`,
      `CREATE POLICY "authenticated_update_product_variants" ON public.product_variants FOR UPDATE USING (auth.uid() IS NOT NULL) WITH CHECK (auth.uid() IS NOT NULL)`,
      `CREATE POLICY "authenticated_delete_product_variants" ON public.product_variants FOR DELETE USING (auth.uid() IS NOT NULL)`,
      
      // Variant assets policies
      `CREATE POLICY "authenticated_insert_variant_assets" ON public.variant_assets FOR INSERT WITH CHECK (auth.uid() IS NOT NULL)`,
      `CREATE POLICY "authenticated_update_variant_assets" ON public.variant_assets FOR UPDATE USING (auth.uid() IS NOT NULL) WITH CHECK (auth.uid() IS NOT NULL)`,
      `CREATE POLICY "authenticated_delete_variant_assets" ON public.variant_assets FOR DELETE USING (auth.uid() IS NOT NULL)`
    ]
    
    for (let i = 0; i < policies.length; i++) {
      const policy = policies[i]
      console.log(`⚡ Executing policy ${i + 1}/${policies.length}...`)
      
      try {
        // Use raw SQL execution
        const { error } = await supabase.rpc('exec_sql', { sql: policy })
        
        if (error && !error.message.includes('already exists')) {
          console.error(`❌ Error executing policy ${i + 1}:`, error.message)
        } else {
          console.log(`✅ Policy ${i + 1} applied successfully`)
        }
      } catch (err) {
        console.error(`❌ Exception executing policy ${i + 1}:`, err.message)
      }
    }
    
    console.log('🎉 All policies processed!')
    
  } catch (error) {
    console.error('❌ Failed to apply policies:', error.message)
  }
}

// Run the script
if (require.main === module) {
  applyPoliciesDirectly()
    .then(() => process.exit(0))
    .catch(() => process.exit(1))
}

module.exports = { applyAdminPolicies, applyPoliciesDirectly }
