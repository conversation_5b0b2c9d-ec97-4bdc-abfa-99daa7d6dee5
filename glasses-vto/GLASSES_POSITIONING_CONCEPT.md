# Konsep P<PERSON>sangan Kacamata - Glasses Positioning Concept

## Overview
Dokumen ini menjelaskan bagaimana sistem positioning kacamata bekerja dan bagaimana blue dots (face landmarks) digunakan untuk alignment yang tepat.

## Konsep Dasar Positioning

### 1. MediaPipe Face Landmarks
MediaPipe mendeteksi 468 titik landmark pada wajah. <PERSON> paling penting untuk kacamata:

- **Point 143 (leftEye)**: Titik tengah mata kiri
- **Point 372 (rightEye)**: Titik tengah mata kanan  
- **Point 168 (midEye)**: Titik tengah antara kedua mata (nose bridge)

### 2. Blue Dots dalam Debug Mode
Blue dots yang muncul dalam debug mode menunjukkan:
- **Dot kiri**: leftEye landmark (143) - posisi mata kiri
- **Dot kanan**: rightEye landmark (372) - posisi mata kanan
- **Dot tengah**: midEye landmark (168) - titik pemasangan kacamata

## Sistem Koordinat

### MediaPipe Coordinates
- **Range**: 0.0 - 1.0 (normalized)
- **Origin**: Top-left corner (0,0)
- **X**: 0 = kiri, 1 = kanan
- **Y**: 0 = atas, 1 = bawah

### Screen Coordinates  
- **Range**: 0 - videoWidth, 0 - videoHeight
- **Conversion**: `screenX = normalizedX * videoWidth`

### Three.js World Coordinates
- **Camera Position**: (width/2, -height/2, z)
- **Camera Target**: (width/2, -height/2, 0)
- **Glasses Position**: Mapped from screen coordinates

## Formula Positioning

### Coordinate Conversion
```javascript
// Step 1: MediaPipe normalized → Screen coordinates
const screenX = normalizedX * videoWidth
const screenY = normalizedY * videoHeight

// Step 2: Screen → Three.js world coordinates
const worldX = screenX
const worldY = -(screenY - (videoHeight / 2))
const worldZ = -150 // Distance from camera
```

### Perfect Alignment Formula
```javascript
// Calculate center between eyes
const eyeCenterX = (leftEye.x + rightEye.x) / 2
const eyeCenterY = (leftEye.y + rightEye.y) / 2

// This should match midEye landmark (168)
// Difference should be < 0.01 for perfect alignment
```

## Konsep Pemasangan yang Benar

### 1. Alignment dengan Blue Dots
- Kacamata harus tepat berada di **midEye landmark (168)**
- Frame kacamata harus **sejajar** dengan garis leftEye-rightEye
- **Lensa kiri** harus tepat di atas **leftEye landmark (143)**
- **Lensa kanan** harus tepat di atas **rightEye landmark (372)**

### 2. Positioning Logic
```
IF blue dots menunjukkan:
  - Left eye di (100, 200)
  - Right eye di (300, 200)  
  - Mid eye di (200, 200)

THEN kacamata harus:
  - Center frame di (200, 200)
  - Left lens di (150, 200)
  - Right lens di (250, 200)
  - Rotation = 0° (horizontal)
```

### 3. Troubleshooting Alignment

#### Masalah Umum:
1. **Kacamata terlalu tinggi/rendah**:
   - Check Y coordinate conversion
   - Verify camera positioning
   - Adjust vertical offset

2. **Kacamata terlalu kiri/kanan**:
   - Check X coordinate mapping
   - Verify screen-to-world conversion
   - Check for mirroring issues

3. **Kacamata tidak sejajar**:
   - Check rotation calculation
   - Verify eye distance calculation
   - Check roll/pitch/yaw values

## Debug Commands untuk Testing

### 1. Test Center Position
```javascript
window.debugGlasses.testCenter()
// Should place glasses at screen center (320, 240)
```

### 2. Test Face Position
```javascript
window.debugGlasses.testFace(0.5, 0.4)
// Should place glasses at normalized position (0.5, 0.4)
// This equals screen position (320, 192) for 640x480 video
```

### 3. Manual Position Test
```javascript
window.debugGlasses.testManual(320, 0, -150, 30)
// Direct world coordinates: X=320, Y=0, Z=-150, Scale=30
```

## Expected Behavior

### Perfect Alignment:
- Blue dots dan kacamata **overlap** sempurna
- Lensa kiri tepat di blue dot mata kiri
- Lensa kanan tepat di blue dot mata kanan
- Frame center tepat di blue dot tengah (midEye)

### Debug Output:
```
👓 GLASSES POSITIONING DEBUG:
leftEye: { x: 0.250, y: 0.400 }
rightEye: { x: 0.750, y: 0.400 }
calculatedCenter: { x: 0.500, y: 0.400 }
midEyeLandmark: { x: 0.500, y: 0.400 }
centerDifference: { x: 0.0000, y: 0.0000 }
```

### Coordinate Conversion:
```
🎯 COORDINATE CONVERSION:
normalized: { x: 0.500, y: 0.400 }
screen: { x: 320.0, y: 192.0 }
world: { x: 320.0, y: 48.0 }
formula: worldY = -(192.0 - 240) = 48.0
```

## Quality Assurance

### Checklist untuk Perfect Positioning:
- [ ] Blue dots visible dan stabil
- [ ] Kacamata center tepat di midEye dot
- [ ] Lensa kiri align dengan leftEye dot  
- [ ] Lensa kanan align dengan rightEye dot
- [ ] Rotation mengikuti orientasi wajah
- [ ] Scale proporsional dengan jarak mata
- [ ] Tracking smooth saat kepala bergerak

### Performance Metrics:
- **FPS**: > 25 fps untuk smooth tracking
- **Alignment Error**: < 5 pixels dari blue dots
- **Tracking Stability**: < 2 pixels jitter
- **Detection Rate**: > 95% face detection success

## Advanced Features

### 1. Adaptive Positioning
- Auto-adjust berdasarkan bentuk wajah
- Kompensasi untuk berbagai ukuran kepala
- Dynamic scaling berdasarkan jarak kamera

### 2. Multi-Point Calibration
- Gunakan multiple landmarks untuk akurasi
- Weighted average dari beberapa reference points
- Fallback positioning jika landmark hilang

### 3. Temporal Smoothing
- Smooth tracking dengan frame history
- Reduce jitter dengan filtering
- Predictive positioning untuk responsiveness

## Kesimpulan

Konsep pemasangan kacamata yang benar adalah:
1. **Blue dots = Reference points** untuk positioning
2. **Perfect alignment** = kacamata tepat di blue dots
3. **Coordinate conversion** yang akurat dari MediaPipe ke Three.js
4. **Real-time tracking** yang smooth dan responsive

Dengan pemahaman konsep ini, debugging dan fine-tuning positioning menjadi lebih mudah dan akurat.
