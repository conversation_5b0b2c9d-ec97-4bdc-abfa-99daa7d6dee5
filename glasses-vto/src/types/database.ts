export interface Database {
  public: {
    Tables: {
      products: {
        Row: {
          id: string
          slug: string
          name: string
          description: string | null
          brand: string | null
          price_cents: number
          active: boolean
          created_at: string
          updated_at: string
        }
        Insert: Omit<Database['public']['Tables']['products']['Row'], 'id' | 'created_at' | 'updated_at'> & {
          id?: string
          created_at?: string
          updated_at?: string
        }
        Update: Partial<Database['public']['Tables']['products']['Insert']>
      }
      product_variants: {
        Row: {
          id: string
          product_id: string
          sku: string | null
          color: string | null
          size: string | null
          bridge_width_mm: number | null
          temple_length_mm: number | null
          lens_width_mm: number | null
          stock: number | null
          glb_path: string | null
          preview_image_path: string | null
          active: boolean
          created_at: string
        }
        Insert: Omit<Database['public']['Tables']['product_variants']['Row'], 'id' | 'created_at'> & {
          id?: string
          created_at?: string
        }
        Update: Partial<Database['public']['Tables']['product_variants']['Insert']>
      }
      variant_assets: {
        Row: {
          id: string
          variant_id: string
          kind: 'texture' | 'thumbnail' | 'ar-mask' | 'extra'
          path: string
          meta: Record<string, any>
        }
        Insert: Omit<Database['public']['Tables']['variant_assets']['Row'], 'id'> & {
          id?: string
        }
        Update: Partial<Database['public']['Tables']['variant_assets']['Insert']>
      }
      categories: {
        Row: {
          id: string
          slug: string
          name: string
        }
        Insert: Omit<Database['public']['Tables']['categories']['Row'], 'id'> & {
          id?: string
        }
        Update: Partial<Database['public']['Tables']['categories']['Insert']>
      }
      product_categories: {
        Row: {
          product_id: string
          category_id: string
        }
        Insert: Database['public']['Tables']['product_categories']['Row']
        Update: Partial<Database['public']['Tables']['product_categories']['Insert']>
      }
      wishlists: {
        Row: {
          id: string
          user_id: string
          product_id: string
          created_at: string
        }
        Insert: Omit<Database['public']['Tables']['wishlists']['Row'], 'id' | 'created_at'> & {
          id?: string
          created_at?: string
        }
        Update: Partial<Database['public']['Tables']['wishlists']['Insert']>
      }
      tryon_events: {
        Row: {
          id: number
          user_id: string | null
          product_id: string | null
          variant_id: string | null
          device: string | null
          fps: number | null
          width: number | null
          height: number | null
          created_at: string
        }
        Insert: Omit<Database['public']['Tables']['tryon_events']['Row'], 'id' | 'created_at'> & {
          id?: number
          created_at?: string
        }
        Update: Partial<Database['public']['Tables']['tryon_events']['Insert']>
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      toggle_wishlist: {
        Args: {
          p_product_id: string
        }
        Returns: void
      }
      get_catalog: {
        Args: {
          p_search?: string | null
          p_category_slug?: string | null
          p_color?: string | null
          p_price_min?: number | null
          p_price_max?: number | null
          p_limit?: number
          p_offset?: number
        }
        Returns: Database['public']['Tables']['products']['Row'][]
      }
      get_product_details: {
        Args: {
          p_slug: string
        }
        Returns: Array<{
          product_id: string
          product_name: string
          product_description: string | null
          product_brand: string | null
          product_price_cents: number
          variant_id: string | null
          variant_sku: string | null
          variant_color: string | null
          variant_size: string | null
          variant_glb_path: string | null
          variant_preview_image_path: string | null
          variant_stock: number | null
          categories: string[]
        }>
      }
      get_user_wishlist: {
        Args: Record<string, never>
        Returns: Array<{
          wishlist_id: string
          product_id: string
          product_name: string
          product_brand: string | null
          product_price_cents: number
          product_slug: string
          preview_image_path: string | null
          added_at: string
        }>
      }
    }
    Enums: {
      [_ in never]: never
    }
  }
}
