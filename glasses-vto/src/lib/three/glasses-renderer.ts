import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { GlassesModelConfig, getGlassesConfig, applyGlassesConfig } from '../glasses-config'

export class GlassesRenderer {
  private scene: THREE.Scene
  private camera: THREE.PerspectiveCamera
  private renderer: THREE.WebGLRenderer
  private glassesModel: THREE.Group | null = null
  private loader: GLTFLoader
  private animationId: number | null = null
  private currentConfig: GlassesModelConfig | null = null

  constructor(canvas: HTMLCanvasElement, width: number, height: number) {
    console.log('🏗️ Creating GlassesRenderer with:', { width, height, canvas: !!canvas })

    try {
      // Initialize Scene
      this.scene = new THREE.Scene()
      this.scene.background = null // Transparent background
      console.log('✅ Scene created')

      // Initialize Camera (<PERSON>uan's approach for video mode)
      this.camera = new THREE.PerspectiveCamera(
        45, // Field of view (<PERSON>'s setting)
        width / height, // Aspect ratio
        0.1, // Near clipping plane
        2000 // Far clipping plane (increased for better depth)
      )
      console.log('✅ Camera created')

    // FIXED: Set camera position for proper video overlay alignment
    // Position camera to match the coordinate system we use for face landmarks
    this.camera.position.x = width / 2
    this.camera.position.y = -height / 2

    // FIXED: Use closer camera position to prevent object clipping
    // Original formula was too far: -(height / 2) / Math.tan((45 * Math.PI / 180) / 2)
    this.camera.position.z = -300 // Fixed distance to prevent clipping
    this.camera.lookAt(width / 2, -height / 2, 0)

    // Ensure camera projection matrix is updated
    this.camera.updateProjectionMatrix()

      // Initialize Renderer
      this.renderer = new THREE.WebGLRenderer({
        canvas,
        alpha: true,
        antialias: true,
        preserveDrawingBuffer: true // For snapshot functionality
      })
      console.log('✅ WebGL Renderer created')

      this.renderer.setSize(width, height)
      this.renderer.setPixelRatio(window.devicePixelRatio)
      this.renderer.outputColorSpace = THREE.SRGBColorSpace
      this.renderer.setClearColor(0x000000, 0) // Transparent background
      console.log('✅ Renderer configured')

    // Setup Lighting (Benson Ruan's approach)
    const frontLight = new THREE.SpotLight(0xffffff, 0.3)
    frontLight.position.set(10, 10, 10)
    this.scene.add(frontLight)

    const backLight = new THREE.SpotLight(0xffffff, 0.3)
    backLight.position.set(10, 10, -10)
    this.scene.add(backLight)

    // Add camera light
    const cameraLight = new THREE.PointLight(0xffffff, 0.8)
    this.camera.add(cameraLight)
    this.scene.add(this.camera)

    // Initialize Loader
    this.loader = new GLTFLoader()



      console.log('✅ GlassesRenderer initialized successfully with camera at:', this.camera.position)

    } catch (error) {
      console.error('❌ Failed to create GlassesRenderer:', error)
      throw error
    }
  }

  async loadGlassesModel(modelUrl: string, modelId?: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // Remove existing model if any
      if (this.glassesModel) {
        this.scene.remove(this.glassesModel)
        this.glassesModel = null
      }

      // Get configuration for this model
      this.currentConfig = modelId ? getGlassesConfig(modelId) : null

      this.loader.load(
        modelUrl,
        (gltf) => {
          console.log('📦 GLTF loaded successfully:', gltf)
          this.glassesModel = gltf.scene

          // Debug model info
          console.log('👓 Glasses model info:', {
            children: this.glassesModel.children.length,
            position: this.glassesModel.position,
            scale: this.glassesModel.scale,
            visible: this.glassesModel.visible
          })

          // Set initial properties
          let meshCount = 0
          this.glassesModel.traverse((child) => {
            if ((child as THREE.Mesh).isMesh) {
              const mesh = child as THREE.Mesh
              meshCount++
              mesh.castShadow = true
              mesh.receiveShadow = true

              console.log(`🔧 Processing mesh ${meshCount}:`, {
                name: mesh.name,
                geometry: mesh.geometry.type,
                material: mesh.material
              })

              // Ensure materials are double-sided for better rendering
              if (Array.isArray(mesh.material)) {
                mesh.material.forEach(mat => {
                  mat.side = THREE.DoubleSide
                })
              } else {
                mesh.material.side = THREE.DoubleSide
              }
            }
          })

          // Make sure model is visible and positioned correctly
          this.glassesModel.visible = true
          this.glassesModel.position.set(0, 0, -100) // Start with reasonable Z position
          this.glassesModel.scale.set(10, 10, 10)    // Start with visible scale

          console.log(`✅ Processed ${meshCount} meshes, adding to scene`)
          this.scene.add(this.glassesModel)

          console.log('🎬 Scene children after adding model:', this.scene.children.length)
          console.log('👓 Model added to scene:', {
            position: this.glassesModel.position,
            scale: this.glassesModel.scale,
            visible: this.glassesModel.visible,
            boundingBox: this.glassesModel.children[0]?.geometry?.boundingBox
          })

          // TEST: Position glasses at center of screen for initial visibility test
          this.testPositionGlasses()

          resolve()
        },
        (progress) => {
          console.log('Loading progress:', (progress.loaded / progress.total) * 100 + '%')
        },
        (error) => {
          console.error('Error loading model:', error)
          reject(error)
        }
      )
    })
  }

  updateGlassesPosition(position: { x: number; y: number; z: number }) {
    if (!this.glassesModel) return
    
    this.glassesModel.position.set(position.x, position.y, position.z)
  }

  updateGlassesRotation(rotation: { x: number; y: number; z: number }) {
    if (!this.glassesModel) return
    
    this.glassesModel.rotation.set(rotation.x, rotation.y, rotation.z)
  }

  updateGlassesScale(scale: number) {
    if (!this.glassesModel) return
    
    this.glassesModel.scale.set(scale, scale, scale)
  }

  // Update glasses transform from face landmarks
  updateFromFaceLandmarks(keyPoints: any, rotation: any, scale: number, canvasWidth: number = 640, canvasHeight: number = 480) {
    if (!this.glassesModel || !keyPoints) return

    // Calculate center position between eyes with better accuracy
    const centerX = (keyPoints.leftEyeCenter.x + keyPoints.rightEyeCenter.x) / 2
    const centerY = (keyPoints.leftEyeCenter.y + keyPoints.rightEyeCenter.y) / 2

    // Calculate vertical offset to position glasses properly on nose bridge
    const eyeToNoseOffset = keyPoints.noseBridge.y - centerY
    const adjustedY = centerY + (eyeToNoseOffset * 0.3) // Move slightly towards nose bridge

    // Convert normalized coordinates to Three.js coordinates
    // MediaPipe coordinates are normalized 0-1, need to map to Three.js world space
    const aspectRatio = canvasWidth / canvasHeight

    // Map to Three.js coordinate system with proper aspect ratio handling
    const x = (centerX - 0.5) * 8 * aspectRatio  // Horizontal positioning
    const y = -(adjustedY - 0.5) * 8             // Vertical positioning (inverted Y)
    const z = keyPoints.noseBridge.z ? keyPoints.noseBridge.z * 2 : 0 // Depth positioning

    this.updateGlassesPosition({ x, y, z })
    this.updateGlassesRotation(rotation)
    this.updateGlassesScale(scale)
  }

  // Benson Ruan's proven accurate positioning method
  updateFromFaceLandmarksBensonRuan(
    keyPoints: any,
    rotation: any,
    scale: number,
    videoElement: HTMLVideoElement,
    offsets: { x: number; y: number; z: number } = { x: 0, y: 0, z: 0 }
  ) {
    if (!this.glassesModel || !keyPoints || !videoElement) return

    const videoWidth = videoElement.videoWidth
    const videoHeight = videoElement.videoHeight

    // Benson Ruan's method: Use midEye (168) as primary anchor point
    const midEye = keyPoints.midEye
    const positionOffset = { x: 0, y: 0.02, z: 0 } // Small upward offset like data-3d-up

    // Direct coordinate mapping (Benson Ruan's approach)
    const worldX = midEye.x * videoWidth + (offsets.x / 100) * 50
    const worldY = (-midEye.y * videoHeight) + positionOffset.y * videoHeight + (offsets.y / 100) * 50
    const worldZ = (-this.camera.position.z + midEye.z * 100) + (offsets.z / 100) * 20

    // Apply position
    this.updateGlassesPosition({ x: worldX, y: worldY, z: worldZ })

    // Apply rotation with up vector (Benson Ruan's method)
    if (rotation.upVector) {
      this.updateGlassesRotationWithUpVector(rotation, rotation.upVector)
    } else {
      this.updateGlassesRotation(rotation)
    }

    this.updateGlassesScale(scale)
  }

  // Method to handle up vector rotation (Benson Ruan's approach)
  updateGlassesRotationWithUpVector(rotation: any, upVector: any) {
    if (!this.glassesModel) return

    // Set up vector for proper orientation
    this.glassesModel.up.set(upVector.x, upVector.y, upVector.z)

    // Apply rotations
    this.glassesModel.rotation.set(rotation.x, rotation.y, rotation.z)
  }

  // PRECISION method for exact eye alignment using debug landmarks
  updateFromFaceLandmarksHybrid(
    keyPoints: any,
    rotation: any,
    scale: number,
    videoElement: HTMLVideoElement,
    offsets: { x: number; y: number; z: number } = { x: 0, y: 0, z: 0 }
  ) {
    if (!this.glassesModel || !keyPoints || !videoElement) return

    const videoWidth = videoElement.videoWidth
    const videoHeight = videoElement.videoHeight

    // PRECISION: Calculate exact center between leftEye and rightEye for perfect alignment
    // This should match exactly with the blue dots in debug mode
    const leftEye = keyPoints.leftEye   // landmark 143
    const rightEye = keyPoints.rightEye // landmark 372
    const midEye = keyPoints.midEye     // landmark 168

    // KONSEP PEMASANGAN KACAMATA:
    // 1. Blue dots menunjukkan landmark wajah dari MediaPipe
    // 2. leftEye (143) dan rightEye (372) adalah titik tengah mata kiri dan kanan
    // 3. midEye (168) adalah titik tengah antara kedua mata (nose bridge)
    // 4. Kacamata harus dipasang tepat di midEye dengan alignment ke kedua mata

    // Use calculated center between eyes for more accurate positioning
    const eyeCenterX = (leftEye.x + rightEye.x) / 2
    const eyeCenterY = (leftEye.y + rightEye.y) / 2
    const eyeCenterZ = (leftEye.z + rightEye.z) / 2

    // Verify that calculated center matches midEye (should be very close)
    const centerPoint = { x: eyeCenterX, y: eyeCenterY, z: eyeCenterZ }

    // Debug: Show alignment between calculated center and midEye
    if (Math.random() < 0.05) { // 5% chance to log
      console.log('👓 GLASSES POSITIONING DEBUG:', {
        leftEye: { x: leftEye.x.toFixed(3), y: leftEye.y.toFixed(3) },
        rightEye: { x: rightEye.x.toFixed(3), y: rightEye.y.toFixed(3) },
        calculatedCenter: { x: eyeCenterX.toFixed(3), y: eyeCenterY.toFixed(3) },
        midEyeLandmark: { x: midEye.x.toFixed(3), y: midEye.y.toFixed(3) },
        centerDifference: {
          x: Math.abs(eyeCenterX - midEye.x).toFixed(4),
          y: Math.abs(eyeCenterY - midEye.y).toFixed(4)
        },
        note: 'Calculated center should match midEye landmark for perfect alignment'
      })
    }

    // KONSEP KOORDINAT SYSTEM:
    // MediaPipe: normalized coordinates (0-1) where (0,0) = top-left, (1,1) = bottom-right
    // Three.js: world coordinates where camera looks at center of video
    // Blue dots in debug mode show MediaPipe landmarks in screen coordinates

    // Step 1: Convert normalized coordinates to screen pixel coordinates
    const screenX = centerPoint.x * videoWidth
    const screenY = centerPoint.y * videoHeight

    // Step 2: Map to Three.js world coordinates
    // IMPORTANT: Video is mirrored with scaleX(-1), so we need to account for this
    // Camera setup: positioned at (width/2, -height/2, z) looking at (width/2, -height/2, 0)

    // X coordinate: MIRROR CORRECTION - flip X coordinate for mirrored video
    // For mirrored video: screenX=100 should become worldX=540 (640-100)
    let worldX = videoWidth - screenX

    // Y coordinate: MediaPipe Y=0 (top) to Y=1 (bottom)
    // Three.js camera at -height/2, so screen center should be at world Y=0
    // Formula: worldY = -(screenY - videoHeight/2)
    let worldY = -(screenY - (videoHeight / 2))

    // Debug: Show coordinate conversion
    if (Math.random() < 0.02) { // 2% chance to log
      console.log('🎯 COORDINATE CONVERSION (MIRRORED):', {
        normalized: { x: centerPoint.x.toFixed(3), y: centerPoint.y.toFixed(3) },
        screen: { x: screenX.toFixed(1), y: screenY.toFixed(1) },
        world: { x: worldX.toFixed(1), y: worldY.toFixed(1) },
        videoSize: { width: videoWidth, height: videoHeight },
        mirrorFormula: `worldX = ${videoWidth} - ${screenX.toFixed(1)} = ${worldX.toFixed(1)}`,
        yFormula: `worldY = -(${screenY.toFixed(1)} - ${videoHeight/2}) = ${worldY.toFixed(1)}`,
        note: 'X coordinate flipped for mirrored video display'
      })
    }

    // Z positioning: Place glasses slightly in front of face using calculated center
    // Camera is at z=-300, so glasses should be at z=-100 to z=-200 for visibility
    let worldZ = centerPoint.z ? (centerPoint.z * -400) : -100  // Closer to camera, better visibility

    console.log('🎯 PRECISION Position calculation for exact eye alignment:', {
      originalMidEye: midEye,
      calculatedCenter: centerPoint,
      leftEye: leftEye,
      rightEye: rightEye,
      screenCoords: { screenX, screenY },
      worldPosition: { worldX, worldY, worldZ },
      videoWidth,
      videoHeight,
      cameraPosition: this.camera.position,
      yCalculation: {
        screenY,
        videoHeightHalf: videoHeight / 2,
        worldY: worldY,
        explanation: 'worldY = -screenY + (videoHeight/2) for camera alignment'
      },
      shouldAlignWith: 'Exact center between blue dots 143 (leftEye) and 372 (rightEye)',
      eyeDistance: Math.sqrt(Math.pow((rightEye.x - leftEye.x) * videoWidth, 2) + Math.pow((rightEye.y - leftEye.y) * videoHeight, 2))
    })

    // Apply model-specific configuration if available
    if (this.currentConfig) {
      worldX += this.currentConfig.offsetX * videoWidth
      worldY += this.currentConfig.offsetY * videoHeight
      worldZ += this.currentConfig.offsetZ * 100
    }

    // Apply user offsets (scaled appropriately for the video dimensions)
    worldX += (offsets.x / 100) * videoWidth * 0.1  // 10% of video width per 100 offset units
    worldY += (offsets.y / 100) * videoHeight * 0.1 // 10% of video height per 100 offset units
    worldZ += (offsets.z / 100) * 50                // 50 units per 100 offset units

    // Apply position
    this.updateGlassesPosition({ x: worldX, y: worldY, z: worldZ })

    console.log('🎯 Applied position to glasses:', {
      worldPosition: { x: worldX, y: worldY, z: worldZ },
      glassesPosition: this.glassesModel?.position,
      glassesVisible: this.glassesModel?.visible
    })

    // PRECISION: Scale calculation based on eye distance for realistic glasses size
    // Base scale should match the distance between eyes for natural look
    let finalScale = scale * (videoWidth / 640) * 15 // Balanced multiplier for natural size

    if (this.currentConfig) {
      finalScale *= this.currentConfig.scale / 0.015 // Apply model-specific scale
    }

    // Ensure scale is within reasonable bounds for natural glasses appearance
    finalScale = Math.max(10, Math.min(40, finalScale)) // Balanced bounds for natural size

    console.log('📏 PRECISION Scale calculation for natural glasses size:', {
      baseScale: scale,
      finalScale,
      videoWidth,
      configScale: this.currentConfig?.scale,
      targetSize: 'Should match eye distance in debug blue dots'
    })

    // Use enhanced rotation with up vector
    if (rotation.upVector) {
      this.updateGlassesRotationWithUpVector(rotation, rotation.upVector)
    } else {
      this.updateGlassesRotation(rotation)
    }

    this.updateGlassesScale(finalScale)

    // Make sure glasses are visible
    if (this.glassesModel) {
      this.glassesModel.visible = true
      console.log('👓 Final glasses state:', {
        position: this.glassesModel.position,
        scale: this.glassesModel.scale,
        visible: this.glassesModel.visible
      })
    }
  }

  // Change glasses material/color
  updateGlassesMaterial(options: { 
    frameColor?: string; 
    lensOpacity?: number;
    lensColor?: string;
  }) {
    if (!this.glassesModel) return

    this.glassesModel.traverse((child) => {
      if ((child as THREE.Mesh).isMesh) {
        const mesh = child as THREE.Mesh
        
        // Update frame color
        if (options.frameColor && mesh.name.toLowerCase().includes('frame')) {
          if (Array.isArray(mesh.material)) {
            mesh.material.forEach(mat => {
              mat.color = new THREE.Color(options.frameColor)
            })
          } else {
            mesh.material.color = new THREE.Color(options.frameColor)
          }
        }
        
        // Update lens properties
        if (mesh.name.toLowerCase().includes('lens')) {
          if (Array.isArray(mesh.material)) {
            mesh.material.forEach(mat => {
              if (options.lensOpacity !== undefined) {
                mat.opacity = options.lensOpacity
                mat.transparent = true
              }
              if (options.lensColor) {
                mat.color = new THREE.Color(options.lensColor)
              }
            })
          } else {
            if (options.lensOpacity !== undefined) {
              mesh.material.opacity = options.lensOpacity
              mesh.material.transparent = true
            }
            if (options.lensColor) {
              mesh.material.color = new THREE.Color(options.lensColor)
            }
          }
        }
      }
    })
  }

  render() {
    try {
      this.renderer.render(this.scene, this.camera)

      // Debug canvas state
      const canvas = this.renderer.domElement
      if (Math.random() < 0.005) { // 0.5% chance to log
        const clearColor = this.renderer.getClearColor()
        console.log('🎨 Canvas render debug:', {
          canvasSize: `${canvas.width}x${canvas.height}`,
          canvasStyle: `${canvas.style.width}x${canvas.style.height}`,
          pixelRatio: this.renderer.getPixelRatio(),
          clearColor: clearColor ? clearColor.getHex() : 'transparent',
          clearAlpha: this.renderer.getClearAlpha()
        })
      }

      // Enhanced debug logging for positioning issues
      if (this.glassesModel) {
        if (!this.glassesModel.visible) {
          console.warn('⚠️ Glasses model loaded but not visible')
        }

        // Log position occasionally for debugging
        if (Math.random() < 0.01) { // 1% chance to log
          console.log('👓 Glasses render state:', {
            visible: this.glassesModel.visible,
            position: this.glassesModel.position,
            scale: this.glassesModel.scale,
            rotation: this.glassesModel.rotation,
            sceneChildren: this.scene.children.length,
            cameraPosition: this.camera.position,
            modelInScene: this.scene.children.includes(this.glassesModel)
          })
        }
      } else {
        // Log if no model is loaded
        if (Math.random() < 0.01) { // 1% chance to log
          console.warn('⚠️ No glasses model loaded for rendering')
        }
      }
    } catch (renderError) {
      console.error('❌ Render error:', renderError)
    }
  }

  startRenderLoop(callback?: () => void) {
    const animate = () => {
      this.animationId = requestAnimationFrame(animate)
      
      if (callback) callback()
      
      this.render()
    }
    animate()
  }

  stopRenderLoop() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
  }

  resize(width: number, height: number) {
    // Update camera for video overlay (Benson Ruan's method)
    this.camera.aspect = width / height
    this.camera.position.x = width / 2
    this.camera.position.y = -height / 2

    // FIXED: Use consistent closer camera position
    this.camera.position.z = -300 // Fixed distance to prevent clipping
    this.camera.lookAt(width / 2, -height / 2, 0)
    this.camera.updateProjectionMatrix()

    this.renderer.setSize(width, height)
    console.log('Camera resized to:', { width, height, position: this.camera.position })
  }

  getSnapshot(): string {
    this.render() // Ensure fresh render
    return this.renderer.domElement.toDataURL('image/png')
  }

  // TEST: Position glasses at center of screen for initial visibility test
  testPositionGlasses() {
    if (!this.glassesModel) {
      console.warn('❌ No glasses model available for test positioning')
      return
    }

    // Position glasses at center of the video frame using proper coordinates
    // For 1280x720 video, center should be at (640, -360)
    const centerX = this.camera.position.x // Should be videoWidth/2
    const centerY = this.camera.position.y // Should be -videoHeight/2
    const testZ = -200 // Position in front of camera

    this.glassesModel.position.set(centerX, centerY, testZ)
    this.glassesModel.scale.set(30, 30, 30) // More visible scale
    this.glassesModel.visible = true

    console.log('🧪 TEST: Positioned glasses at center for visibility test:', {
      position: this.glassesModel.position,
      scale: this.glassesModel.scale,
      visible: this.glassesModel.visible,
      cameraPosition: this.camera.position,
      expectedCenter: { x: centerX, y: centerY, z: testZ },
      sceneChildren: this.scene.children.length,
      modelInScene: this.scene.children.includes(this.glassesModel)
    })

    // Force immediate render
    this.render()
    console.log('🎨 Forced render after test positioning')
  }

  // Manual test method to position glasses at specific coordinates
  manualTestPosition(x: number, y: number, z: number, scale: number = 5) {
    if (!this.glassesModel) {
      console.warn('No glasses model loaded for manual test')
      return
    }

    // FIXED: Use smaller, more appropriate scale
    const appropriateScale = scale * 0.2 // Reduce scale significantly

    this.glassesModel.position.set(x, y, z)
    this.glassesModel.scale.set(appropriateScale, appropriateScale, appropriateScale)
    this.glassesModel.visible = true

    console.log('🔧 MANUAL TEST: Positioned glasses at:', {
      position: { x, y, z },
      requestedScale: scale,
      actualScale: appropriateScale,
      glassesPosition: this.glassesModel.position,
      glassesScale: this.glassesModel.scale,
      visible: this.glassesModel.visible,
      cameraPosition: this.camera.position,
      note: 'Scale reduced to prevent oversized glasses'
    })

    // Force a render
    this.render()
  }

  // Method to test positioning at face coordinates (for debugging)
  testFacePosition(normalizedX: number = 0.5, normalizedY: number = 0.4, videoWidth: number = 1280, videoHeight: number = 720) {
    if (!this.glassesModel) {
      console.warn('No glasses model loaded for face position test')
      return
    }

    // Convert normalized coordinates to world coordinates with MIRROR CORRECTION
    const screenX = normalizedX * videoWidth
    const screenY = normalizedY * videoHeight

    // MIRROR CORRECTION: Flip X coordinate for mirrored video
    const worldX = videoWidth - screenX
    const worldY = -(screenY - (videoHeight / 2))  // PRECISION Y calculation
    const worldZ = -100 // Closer to camera for better visibility

    this.manualTestPosition(worldX, worldY, worldZ, 5) // Reduced scale

    console.log('👤 PRECISION FACE POSITION TEST (MIRRORED):', {
      normalizedCoords: { x: normalizedX, y: normalizedY },
      screenCoords: { x: screenX, y: screenY },
      worldCoords: { x: worldX, y: worldY, z: worldZ },
      videoSize: { width: videoWidth, height: videoHeight },
      mirrorFormula: `worldX = ${videoWidth} - ${screenX} = ${worldX}`,
      yCalculation: `worldY = -(${screenY} - ${videoHeight/2}) = ${worldY}`,
      note: 'X coordinate flipped for mirrored video - should align with blue dots'
    })
  }

  // Toggle background for debugging
  toggleDebugBackground() {
    if (this.scene.background === null) {
      this.scene.background = new THREE.Color(0x404040) // Dark gray
      console.log('🎨 Debug background enabled (dark gray)')
    } else {
      this.scene.background = null
      console.log('🎨 Debug background disabled (transparent)')
    }
    this.render()
  }

  // Debug method to check canvas and renderer state
  debugCanvasState() {
    const canvas = this.renderer.domElement
    console.log('🔍 CANVAS DEBUG STATE:', {
      canvas: {
        width: canvas.width,
        height: canvas.height,
        styleWidth: canvas.style.width,
        styleHeight: canvas.style.height,
        clientWidth: canvas.clientWidth,
        clientHeight: canvas.clientHeight,
        offsetWidth: canvas.offsetWidth,
        offsetHeight: canvas.offsetHeight
      },
      renderer: {
        pixelRatio: this.renderer.getPixelRatio(),
        size: (() => {
          const size = new THREE.Vector2()
          this.renderer.getSize(size)
          return { width: size.x, height: size.y }
        })(),
        clearColor: this.renderer.getClearColor()?.getHex?.() || 'undefined',
        clearAlpha: this.renderer.getClearAlpha(),
        shadowMapEnabled: this.renderer.shadowMap.enabled
      },
      scene: {
        children: this.scene.children.length,
        background: this.scene.background,
        hasModel: !!this.glassesModel,
        modelVisible: this.glassesModel?.visible
      },
      camera: {
        position: this.camera.position,
        rotation: this.camera.rotation,
        fov: this.camera.fov,
        aspect: this.camera.aspect,
        near: this.camera.near,
        far: this.camera.far
      }
    })

    // Test render with simple geometry
    this.testRenderSimpleGeometry()
  }

  // Test method to render simple geometry to verify canvas works
  testRenderSimpleGeometry() {
    console.log('🧪 Testing simple geometry render...')

    try {
      // Create a simple red cube for testing
      const geometry = new THREE.BoxGeometry(50, 50, 50)
      const material = new THREE.MeshBasicMaterial({ color: 0xff0000 })
      const testCube = new THREE.Mesh(geometry, material)

      // Position cube at camera center
      testCube.position.set(this.camera.position.x, this.camera.position.y, -300)

      this.scene.add(testCube)

      console.log('🧪 Test cube added at:', testCube.position)
      console.log('🧪 Scene children count:', this.scene.children.length)

      // Force render
      this.render()

      console.log('🧪 Test cube rendered successfully')

      // Remove test cube after 3 seconds
      setTimeout(() => {
        this.scene.remove(testCube)
        console.log('🧪 Test cube removed')
      }, 3000)

    } catch (error) {
      console.error('❌ Error in test geometry render:', error)
    }
  }

  // Simple method to render basic glasses shape without GLTF
  renderSimpleGlassesShape() {
    console.log('🧪 Rendering simple glasses shape...')

    try {
      // Remove existing glasses model
      if (this.glassesModel) {
        this.scene.remove(this.glassesModel)
      }

      // Create simple glasses using basic geometry
      const glassesGroup = new THREE.Group()

      // Left lens (circle)
      const leftLensGeometry = new THREE.RingGeometry(15, 20, 16)
      const lensMaterial = new THREE.MeshBasicMaterial({
        color: 0x333333,
        side: THREE.DoubleSide,
        transparent: true,
        opacity: 0.8
      })
      const leftLens = new THREE.Mesh(leftLensGeometry, lensMaterial)
      leftLens.position.set(-25, 0, 0)
      glassesGroup.add(leftLens)

      // Right lens (circle)
      const rightLensGeometry = new THREE.RingGeometry(15, 20, 16)
      const rightLens = new THREE.Mesh(rightLensGeometry, lensMaterial)
      rightLens.position.set(25, 0, 0)
      glassesGroup.add(rightLens)

      // Bridge
      const bridgeGeometry = new THREE.BoxGeometry(10, 3, 2)
      const frameMaterial = new THREE.MeshBasicMaterial({ color: 0x222222 })
      const bridge = new THREE.Mesh(bridgeGeometry, frameMaterial)
      bridge.position.set(0, 0, 0)
      glassesGroup.add(bridge)

      // Position glasses at center
      glassesGroup.position.set(this.camera.position.x, this.camera.position.y, -200)
      glassesGroup.scale.set(2, 2, 2)

      this.scene.add(glassesGroup)
      this.glassesModel = glassesGroup

      console.log('🧪 Simple glasses shape created and positioned at:', glassesGroup.position)

      // Force render
      this.render()

    } catch (error) {
      console.error('❌ Error creating simple glasses shape:', error)
    }
  }

  dispose() {
    this.stopRenderLoop()

    if (this.glassesModel) {
      this.scene.remove(this.glassesModel)
      this.glassesModel = null
    }

    this.renderer.dispose()
    this.scene.clear()
  }
}