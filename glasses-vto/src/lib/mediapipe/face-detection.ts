import { <PERSON><PERSON><PERSON>marker, FilesetResolver, FaceLandmarkerResult } from '@mediapipe/tasks-vision'

export class FaceDetection {
  private faceLandmarker: FaceLandmarker | null = null
  private isInitialized = false
  private lastVideoTime = -1
  private errorCount = 0
  private maxErrors = 5

  async initialize() {
    if (this.isInitialized) return

    try {
      console.log('Initializing MediaPipe FaceLandmarker...')

      const vision = await FilesetResolver.forVisionTasks(
        'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@latest/wasm'
      )

      this.faceLandmarker = await FaceLandmarker.createFromOptions(vision, {
        baseOptions: {
          modelAssetPath: 'https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task',
          delegate: 'CPU' // CPU is more stable than GPU
        },
        outputFaceBlendshapes: false, // Disabled for performance
        outputFacialTransformationMatrixes: false, // Disabled for performance
        runningMode: 'VIDEO',
        numFaces: 1,
        minFaceDetectionConfidence: 0.5, // Lower threshold for better detection
        minFacePresenceConfidence: 0.5,
        minTrackingConfidence: 0.5
      })

      this.isInitialized = true
      console.log('MediaPipe FaceLandmarker initialized successfully')
    } catch (error) {
      console.error('Failed to initialize MediaPipe:', error)

      // Fallback: Try with different configuration
      try {
        console.log('Trying fallback initialization...')
        const vision = await FilesetResolver.forVisionTasks(
          'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@latest/wasm'
        )

        this.faceLandmarker = await FaceLandmarker.createFromOptions(vision, {
          baseOptions: {
            modelAssetPath: 'https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task'
          },
          runningMode: 'VIDEO',
          numFaces: 1
        })

        this.isInitialized = true
        console.log('MediaPipe FaceLandmarker initialized with fallback config')
      } catch (fallbackError) {
        console.error('Fallback initialization also failed:', fallbackError)
        throw fallbackError
      }
    }
  }

  detectFaces(video: HTMLVideoElement): FaceLandmarkerResult | null {
    if (!this.faceLandmarker || !this.isInitialized) {
      console.warn('Face detector not initialized')
      return null
    }

    // Comprehensive video validation
    if (!video) {
      console.warn('Video element is null')
      return null
    }

    // Check video readiness
    if (video.readyState < video.HAVE_ENOUGH_DATA) {
      console.warn(`Video not ready: readyState=${video.readyState}, expected=${video.HAVE_ENOUGH_DATA}`)
      return null
    }

    // Critical: Ensure video has valid dimensions (fixes ROI error)
    if (video.videoWidth <= 0 || video.videoHeight <= 0) {
      console.warn(`Invalid video dimensions: ${video.videoWidth}x${video.videoHeight}`)
      return null
    }

    // Check if video is actually playing
    if (video.paused || video.ended) {
      console.warn('Video is paused or ended')
      return null
    }

    const timestamp = performance.now()

    // Skip if same frame (with small tolerance)
    if (timestamp - this.lastVideoTime < 16) { // ~60fps limit
      return null
    }

    this.lastVideoTime = timestamp

    try {
      // Additional validation before MediaPipe call
      const canvas = document.createElement('canvas')
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      if (canvas.width <= 0 || canvas.height <= 0) {
        console.warn(`Invalid canvas dimensions: ${canvas.width}x${canvas.height}`)
        return null
      }

      // Draw video frame to ensure it's valid
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        console.warn('Cannot get canvas context')
        return null
      }

      ctx.drawImage(video, 0, 0)

      // Now call MediaPipe with validated video
      const results = this.faceLandmarker.detectForVideo(video, timestamp)

      // Log successful detection for debugging
      if (results && results.faceLandmarks && results.faceLandmarks.length > 0) {
        console.log(`✅ Detected ${results.faceLandmarks.length} face(s) at ${video.videoWidth}x${video.videoHeight}`)
      } else {
        console.log(`⚪ No faces detected at ${video.videoWidth}x${video.videoHeight}`)
      }

      return results
    } catch (error) {
      console.error('❌ Face detection error:', error)

      // Log detailed error information
      console.error('Video state:', {
        readyState: video.readyState,
        videoWidth: video.videoWidth,
        videoHeight: video.videoHeight,
        paused: video.paused,
        ended: video.ended,
        currentTime: video.currentTime
      })

      // Try to reinitialize if detection fails repeatedly
      if (error.message && error.message.includes('Invalid timestamp')) {
        console.log('Resetting timestamp due to invalid timestamp error')
        this.lastVideoTime = -1
      }

      // Reset on ROI errors
      if (error.message && error.message.includes('roi->width > 0')) {
        console.log('ROI error detected, resetting...')
        this.lastVideoTime = -1
        this.errorCount++

        // If too many errors, try to reinitialize
        if (this.errorCount >= this.maxErrors) {
          console.warn('Too many errors, attempting to reinitialize MediaPipe...')
          this.reset()
        }
      }

      return null
    }
  }

  // Reset MediaPipe instance
  reset() {
    console.log('Resetting MediaPipe FaceLandmarker...')
    this.faceLandmarker = null
    this.isInitialized = false
    this.lastVideoTime = -1
    this.errorCount = 0
  }

  // Force reinitialize
  async reinitialize() {
    this.reset()
    await this.initialize()
  }

  // Get key face points for glasses positioning (Based on Benson Ruan's accurate method)
  getFaceKeyPoints(landmarks: any) {
    if (!landmarks || landmarks.length === 0) return null

    const face = landmarks[0]

    return {
      // Benson Ruan's proven accurate keypoints
      midEye: face[168],        // Middle between Eyes - PRIMARY ANCHOR POINT
      leftEye: face[143],       // Left Eye - for scaling calculation
      rightEye: face[372],      // Right Eye - for scaling calculation
      noseBottom: face[2],      // Bottom of Nose - for up vector calculation

      // Additional MediaPipe points for enhanced accuracy
      leftEyeOuter: face[33],   // Left eye outer corner
      rightEyeOuter: face[263], // Right eye outer corner
      leftEyeInner: face[133],  // Left eye inner corner
      rightEyeInner: face[362], // Right eye inner corner

      // Eye centers (fallback calculation)
      leftEyeCenter: face[468] || this.calculateEyeCenter(face, 'left'),
      rightEyeCenter: face[473] || this.calculateEyeCenter(face, 'right'),

      // Nose bridge points
      noseBridge: face[6],      // Nose bridge top
      noseTip: face[1],         // Nose tip
      noseCenter: face[168],    // Same as midEye - nose center

      // Face outline for rotation calculation
      chin: face[152],          // Chin bottom
      forehead: face[10],       // Forehead center

      // Temples for arms positioning
      leftTemple: face[54],     // Left temple
      rightTemple: face[284],   // Right temple

      // Additional reference points
      leftCheek: face[234],     // Left cheek
      rightCheek: face[454],    // Right cheek
    }
  }

  // Calculate eye center if not available in landmarks
  private calculateEyeCenter(face: any, eye: 'left' | 'right') {
    if (eye === 'left') {
      // Average of key left eye points
      const points = [face[33], face[7], face[163], face[144], face[145], face[153]]
      return this.averagePoints(points)
    } else {
      // Average of key right eye points
      const points = [face[362], face[398], face[384], face[385], face[386], face[387]]
      return this.averagePoints(points)
    }
  }

  // Helper to average multiple points
  private averagePoints(points: any[]) {
    const sum = points.reduce((acc, point) => ({
      x: acc.x + point.x,
      y: acc.y + point.y,
      z: acc.z + (point.z || 0)
    }), { x: 0, y: 0, z: 0 })

    return {
      x: sum.x / points.length,
      y: sum.y / points.length,
      z: sum.z / points.length
    }
  }

  // Calculate face rotation angles using Benson Ruan's method
  calculateFaceRotation(keyPoints: any) {
    if (!keyPoints) return { x: 0, y: 0, z: 0, upVector: { x: 0, y: 1, z: 0 } }

    // Calculate up vector from midEye to noseBottom (Benson Ruan's method)
    const upVector = {
      x: keyPoints.midEye.x - keyPoints.noseBottom.x,
      y: -(keyPoints.midEye.y - keyPoints.noseBottom.y), // Invert Y
      z: keyPoints.midEye.z - keyPoints.noseBottom.z
    }

    // Normalize up vector
    const upLength = Math.sqrt(upVector.x ** 2 + upVector.y ** 2 + upVector.z ** 2)
    if (upLength > 0) {
      upVector.x /= upLength
      upVector.y /= upLength
      upVector.z /= upLength
    }

    // Calculate roll rotation from up vector (Benson Ruan's method)
    const roll = Math.PI / 2 - Math.acos(upVector.x)

    // Calculate yaw (left-right rotation) using eye positions
    const eyeDistance = Math.sqrt(
      Math.pow(keyPoints.rightEye.x - keyPoints.leftEye.x, 2) +
      Math.pow(keyPoints.rightEye.y - keyPoints.leftEye.y, 2)
    )

    const centerX = (keyPoints.leftEye.x + keyPoints.rightEye.x) / 2
    const noseOffset = keyPoints.midEye.x - centerX
    const yaw = Math.atan2(noseOffset, eyeDistance) * 1.5

    // Calculate pitch (up-down rotation)
    const faceHeight = Math.abs(keyPoints.forehead.y - keyPoints.chin.y)
    const noseHeight = keyPoints.midEye.y - keyPoints.forehead.y
    const pitch = Math.atan2(noseHeight, faceHeight) - 0.2

    return {
      x: pitch,
      y: yaw + Math.PI, // Add PI for proper orientation (Benson Ruan's method)
      z: roll,
      upVector: upVector
    }
  }

  // Calculate glasses scale using Benson Ruan's proven method
  calculateGlassesScale(keyPoints: any) {
    if (!keyPoints) return 1

    // Benson Ruan's method: Use distance between leftEye and rightEye
    const eyeDistance = Math.sqrt(
      (keyPoints.leftEye.x - keyPoints.rightEye.x) ** 2 +
      (keyPoints.leftEye.y - keyPoints.rightEye.y) ** 2 +
      (keyPoints.leftEye.z - keyPoints.rightEye.z) ** 2
    )

    // Base scale multiplier (can be adjusted per glasses model)
    // This matches Benson Ruan's approach where scale is stored as data attribute
    const baseScaleMultiplier = 0.012 // Balanced for natural glasses size

    const calculatedScale = eyeDistance * baseScaleMultiplier
    const clampedScale = Math.max(0.3, Math.min(3.0, calculatedScale)) // Clamp for safety

    // Debug log for scale calculation
    if (Math.random() < 0.02) { // 2% chance to log
      console.log('📏 Scale calculation debug:', {
        eyeDistance,
        baseScaleMultiplier,
        calculatedScale,
        clampedScale,
        leftEye: keyPoints.leftEye,
        rightEye: keyPoints.rightEye
      })
    }

    return clampedScale
  }

  // Additional method for getting position offset (like Benson Ruan's data-3d-up)
  calculatePositionOffset(keyPoints: any) {
    if (!keyPoints) return { x: 0, y: 0, z: 0 }

    // Calculate offset based on face proportions
    const eyeToNoseDistance = Math.sqrt(
      (keyPoints.midEye.x - keyPoints.noseBottom.x) ** 2 +
      (keyPoints.midEye.y - keyPoints.noseBottom.y) ** 2 +
      (keyPoints.midEye.z - keyPoints.noseBottom.z) ** 2
    )

    return {
      x: 0,
      y: eyeToNoseDistance * 0.1, // Small upward offset
      z: 0
    }
  }

  dispose() {
    if (this.faceLandmarker) {
      this.faceLandmarker.close()
      this.faceLandmarker = null
    }
    this.isInitialized = false
  }
}