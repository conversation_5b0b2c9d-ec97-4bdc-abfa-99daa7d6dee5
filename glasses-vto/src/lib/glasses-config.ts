// Glasses model configuration (inspired by <PERSON>'s data attributes approach)

export interface GlassesModelConfig {
  id: string
  name: string
  modelPath: string
  scale: number        // Base scale multiplier (like <PERSON>'s data-3d-scale)
  offsetX: number      // Horizontal offset
  offsetY: number      // Vertical offset (like <PERSON>'s data-3d-up)
  offsetZ: number      // Depth offset
  rotationX: number    // Additional X rotation
  rotationY: number    // Additional Y rotation
  rotationZ: number    // Additional Z rotation
}

// Default configurations for different glasses types
export const GLASSES_CONFIGS: Record<string, GlassesModelConfig> = {
  'default': {
    id: 'default',
    name: 'Default Glasses',
    modelPath: '/models/default-glasses.gltf',
    scale: 0.015,        // Base scale multiplier
    offsetX: 0,          // No horizontal offset
    offsetY: 0.02,       // Small upward offset (like data-3d-up)
    offsetZ: 0,          // No depth offset
    rotationX: 0,        // No additional rotation
    rotationY: 0,
    rotationZ: 0
  },
  
  'aviator': {
    id: 'aviator',
    name: 'Aviator Sunglasses',
    modelPath: '/models/aviator-sunglasses.glb',
    scale: 0.018,        // Slightly larger for aviator style
    offsetX: 0,
    offsetY: 0.015,      // Less upward offset
    offsetZ: 0.005,      // Slightly forward
    rotationX: 0,
    rotationY: 0,
    rotationZ: 0
  },
  
  'round': {
    id: 'round',
    name: 'Round Glasses',
    modelPath: '/models/round-glasses.glb',
    scale: 0.014,        // Smaller for round style
    offsetX: 0,
    offsetY: 0.025,      // Higher position
    offsetZ: 0,
    rotationX: 0,
    rotationY: 0,
    rotationZ: 0
  },
  
  'sport': {
    id: 'sport',
    name: 'Sport Glasses',
    modelPath: '/models/sport-glasses.glb',
    scale: 0.016,        // Medium scale
    offsetX: 0,
    offsetY: 0.01,       // Lower position
    offsetZ: -0.005,     // Slightly back
    rotationX: 0,
    rotationY: 0,
    rotationZ: 0
  },
  
  'cat-eye': {
    id: 'cat-eye',
    name: 'Cat Eye Glasses',
    modelPath: '/models/cat-eye-glasses.glb',
    scale: 0.017,        // Larger for cat eye style
    offsetX: 0,
    offsetY: 0.03,       // Higher position for cat eye
    offsetZ: 0,
    rotationX: 0,
    rotationY: 0,
    rotationZ: 0
  }
}

// Function to get configuration for a specific glasses model
export function getGlassesConfig(modelId: string): GlassesModelConfig {
  return GLASSES_CONFIGS[modelId] || GLASSES_CONFIGS['default']
}

// Function to apply configuration to Three.js glasses model
export function applyGlassesConfig(
  glassesModel: any, 
  config: GlassesModelConfig,
  baseScale: number,
  userOffsets: { x: number; y: number; z: number } = { x: 0, y: 0, z: 0 }
) {
  if (!glassesModel) return

  // Apply scale with configuration
  const finalScale = baseScale * config.scale
  glassesModel.scale.set(finalScale, finalScale, finalScale)

  // Apply position offsets
  const currentPosition = glassesModel.position
  glassesModel.position.set(
    currentPosition.x + config.offsetX + (userOffsets.x / 100),
    currentPosition.y + config.offsetY + (userOffsets.y / 100),
    currentPosition.z + config.offsetZ + (userOffsets.z / 100)
  )

  // Apply additional rotations
  const currentRotation = glassesModel.rotation
  glassesModel.rotation.set(
    currentRotation.x + config.rotationX,
    currentRotation.y + config.rotationY,
    currentRotation.z + config.rotationZ
  )
}

// Function to create configuration from database variant
export function createConfigFromVariant(variant: any): GlassesModelConfig {
  return {
    id: variant.id || 'default',
    name: variant.name || 'Unknown Glasses',
    modelPath: variant.glb_path || '/models/default-glasses.gltf',
    scale: variant.scale || 0.015,
    offsetX: variant.offset_x || 0,
    offsetY: variant.offset_y || 0.02,
    offsetZ: variant.offset_z || 0,
    rotationX: variant.rotation_x || 0,
    rotationY: variant.rotation_y || 0,
    rotationZ: variant.rotation_z || 0
  }
}

// Function to save configuration to database variant
export function saveConfigToVariant(config: GlassesModelConfig) {
  return {
    scale: config.scale,
    offset_x: config.offsetX,
    offset_y: config.offsetY,
    offset_z: config.offsetZ,
    rotation_x: config.rotationX,
    rotation_y: config.rotationY,
    rotation_z: config.rotationZ
  }
}

// Utility function to detect glasses type from model path or name
export function detectGlassesType(modelPath: string, name: string): string {
  const path = modelPath.toLowerCase()
  const glassesName = name.toLowerCase()
  
  if (path.includes('aviator') || glassesName.includes('aviator')) {
    return 'aviator'
  } else if (path.includes('round') || glassesName.includes('round')) {
    return 'round'
  } else if (path.includes('sport') || glassesName.includes('sport')) {
    return 'sport'
  } else if (path.includes('cat') || glassesName.includes('cat')) {
    return 'cat-eye'
  }
  
  return 'default'
}

// Function to get optimal configuration based on face measurements
export function getOptimalConfig(
  baseConfig: GlassesModelConfig,
  faceMetrics: {
    eyeDistance: number
    faceWidth: number
    noseLength: number
  }
): GlassesModelConfig {
  const optimizedConfig = { ...baseConfig }
  
  // Adjust scale based on face width
  if (faceMetrics.faceWidth > 0.6) {
    optimizedConfig.scale *= 1.1 // Larger face, slightly bigger glasses
  } else if (faceMetrics.faceWidth < 0.4) {
    optimizedConfig.scale *= 0.9 // Smaller face, slightly smaller glasses
  }
  
  // Adjust vertical offset based on nose length
  if (faceMetrics.noseLength > 0.1) {
    optimizedConfig.offsetY += 0.01 // Longer nose, move glasses up
  } else if (faceMetrics.noseLength < 0.05) {
    optimizedConfig.offsetY -= 0.01 // Shorter nose, move glasses down
  }
  
  return optimizedConfig
}

// Export default configuration
export const DEFAULT_GLASSES_CONFIG = GLASSES_CONFIGS['default']
