import { createClient } from '@/lib/supabase/server'

/**
 * Get user's wishlist with product details
 */
export async function getUserWishlist() {
  const supabase = await createClient()
  
  const { data, error } = await supabase.rpc('get_user_wishlist')

  if (error) {
    console.error('Error fetching wishlist:', error)
    return { data: null, error }
  }

  return { data, error: null }
}

/**
 * Toggle product in wishlist
 */
export async function toggleWishlist(productId: string) {
  const supabase = await createClient()
  
  const { error } = await supabase.rpc('toggle_wishlist', {
    p_product_id: productId
  })

  if (error) {
    console.error('Error toggling wishlist:', error)
    return { success: false, error }
  }

  return { success: true, error: null }
}

/**
 * Check if product is in wishlist
 */
export async function isInWishlist(productId: string) {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    return { data: false, error: null }
  }

  const { data, error } = await supabase
    .from('wishlists')
    .select('id')
    .eq('user_id', user.id)
    .eq('product_id', productId)
    .single()

  if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
    console.error('Error checking wishlist:', error)
    return { data: false, error }
  }

  return { data: !!data, error: null }
}

/**
 * Add product to wishlist
 */
export async function addToWishlist(productId: string) {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    return { success: false, error: new Error('User not authenticated') }
  }

  const { error } = await supabase
    .from('wishlists')
    .insert({
      user_id: user.id,
      product_id: productId
    })

  if (error) {
    console.error('Error adding to wishlist:', error)
    return { success: false, error }
  }

  return { success: true, error: null }
}

/**
 * Remove product from wishlist
 */
export async function removeFromWishlist(productId: string) {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    return { success: false, error: new Error('User not authenticated') }
  }

  const { error } = await supabase
    .from('wishlists')
    .delete()
    .eq('user_id', user.id)
    .eq('product_id', productId)

  if (error) {
    console.error('Error removing from wishlist:', error)
    return { success: false, error }
  }

  return { success: true, error: null }
}

/**
 * Get wishlist count for current user
 */
export async function getWishlistCount() {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    return { count: 0, error: null }
  }

  const { count, error } = await supabase
    .from('wishlists')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', user.id)

  if (error) {
    console.error('Error fetching wishlist count:', error)
    return { count: 0, error }
  }

  return { count: count || 0, error: null }
}