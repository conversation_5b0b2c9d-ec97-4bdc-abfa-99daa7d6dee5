/**
 * Client-side storage utilities
 * These functions can be used in client components
 */

/**
 * Get public URL for a file in storage
 */
export function getPublicUrl(bucket: string, path: string) {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  return `${supabaseUrl}/storage/v1/object/public/${bucket}/${path}`
}

/**
 * Get product image URL
 */
export function getProductImageUrl(path: string | null) {
  if (!path) return '/placeholder-product.svg'
  return getPublicUrl('products', path)
}

/**
 * Get 3D model URL
 */
export function getModelUrl(path: string | null) {
  if (!path) return null
  return getPublicUrl('models', path)
}

/**
 * Get snapshot URL
 */
export function getSnapshotUrl(path: string | null) {
  if (!path) return null
  return getPublicUrl('snapshots', path)
}