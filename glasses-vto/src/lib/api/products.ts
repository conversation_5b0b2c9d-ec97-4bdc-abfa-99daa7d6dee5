import { createClient } from '@/lib/supabase/server'
import { Database } from '@/types/database'

type Product = Database['public']['Tables']['products']['Row']
type ProductVariant = Database['public']['Tables']['product_variants']['Row']
type Category = Database['public']['Tables']['categories']['Row']

export interface CatalogFilter {
  search?: string
  categorySlug?: string
  color?: string
  priceMin?: number
  priceMax?: number
  limit?: number
  offset?: number
}

export interface ProductWithVariants extends Product {
  variants?: ProductVariant[]
  categories?: Category[]
}

/**
 * Get filtered catalog of products
 */
export async function getCatalog(filters: CatalogFilter = {}) {
  const supabase = await createClient()
  
  const { data, error } = await supabase.rpc('get_catalog', {
    p_search: filters.search || null,
    p_category_slug: filters.categorySlug || null,
    p_color: filters.color || null,
    p_price_min: filters.priceMin || null,
    p_price_max: filters.priceMax || null,
    p_limit: filters.limit || 24,
    p_offset: filters.offset || 0
  })

  if (error) {
    console.error('Error fetching catalog:', error)
    return { data: null, error }
  }

  return { data, error: null }
}

/**
 * Get product details with variants
 */
export async function getProductDetails(slug: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase.rpc('get_product_details', {
    p_slug: slug
  })

  if (error) {
    console.error('Error fetching product details:', error)
    return { data: null, error }
  }

  // Transform the flat data into structured format
  if (data && data.length > 0) {
    const firstRow = data[0]
    const product: ProductWithVariants = {
      id: firstRow.product_id,
      slug,
      name: firstRow.product_name,
      description: firstRow.product_description,
      brand: firstRow.product_brand,
      price_cents: firstRow.product_price_cents,
      active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      variants: data.filter(row => row.variant_id).map(row => ({
        id: row.variant_id!,
        product_id: row.product_id,
        sku: row.variant_sku,
        color: row.variant_color,
        size: row.variant_size,
        bridge_width_mm: null,
        temple_length_mm: null,
        lens_width_mm: null,
        stock: row.variant_stock,
        glb_path: row.variant_glb_path,
        preview_image_path: row.variant_preview_image_path,
        active: true,
        created_at: new Date().toISOString()
      })),
      categories: firstRow.categories.map(name => ({
        id: '',
        slug: name.toLowerCase().replace(/\s+/g, '-'),
        name
      }))
    }

    return { data: product, error: null }
  }

  return { data: null, error: new Error('Product not found') }
}

/**
 * Get all categories
 */
export async function getCategories() {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .order('name')

  if (error) {
    console.error('Error fetching categories:', error)
    return { data: null, error }
  }

  return { data, error: null }
}

/**
 * Get products by category
 */
export async function getProductsByCategory(categorySlug: string) {
  const supabase = await createClient()
  
  const { data: category, error: categoryError } = await supabase
    .from('categories')
    .select('id')
    .eq('slug', categorySlug)
    .single()

  if (categoryError) {
    console.error('Error fetching category:', categoryError)
    return { data: null, error: categoryError }
  }

  const { data, error } = await supabase
    .from('product_categories')
    .select(`
      products (
        *,
        product_variants (*)
      )
    `)
    .eq('category_id', category.id)

  if (error) {
    console.error('Error fetching products by category:', error)
    return { data: null, error }
  }

  const products = data?.map(item => item.products).filter(Boolean) || []
  return { data: products, error: null }
}

/**
 * Search products
 */
export async function searchProducts(query: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      product_variants (*)
    `)
    .or(`name.ilike.%${query}%,description.ilike.%${query}%,brand.ilike.%${query}%`)
    .eq('active', true)
    .order('created_at', { ascending: false })
    .limit(20)

  if (error) {
    console.error('Error searching products:', error)
    return { data: null, error }
  }

  return { data, error: null }
}

/**
 * Get featured products
 */
export async function getFeaturedProducts() {
  const supabase = await createClient()
  
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      product_variants!inner (
        *
      )
    `)
    .eq('active', true)
    .order('created_at', { ascending: false })
    .limit(8)

  if (error) {
    console.error('Error fetching featured products:', error)
    return { data: null, error }
  }

  return { data, error: null }
}