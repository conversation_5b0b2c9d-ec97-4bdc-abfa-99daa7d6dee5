import { createClient } from '@/lib/supabase/server'

/**
 * Get public URL for a file in storage
 */
export function getPublicUrl(bucket: string, path: string) {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  return `${supabaseUrl}/storage/v1/object/public/${bucket}/${path}`
}

/**
 * Upload a snapshot image
 */
export async function uploadSnapshot(file: File, userId: string) {
  const supabase = await createClient()
  
  const timestamp = Date.now()
  const fileName = `${userId}/${timestamp}.jpg`
  
  const { data, error } = await supabase.storage
    .from('snapshots')
    .upload(fileName, file, {
      contentType: 'image/jpeg',
      upsert: false
    })

  if (error) {
    console.error('Error uploading snapshot:', error)
    return { data: null, error }
  }

  const publicUrl = getPublicUrl('snapshots', fileName)
  return { data: { path: fileName, url: publicUrl }, error: null }
}

/**
 * Delete a snapshot image
 */
export async function deleteSnapshot(path: string) {
  const supabase = await createClient()
  
  const { error } = await supabase.storage
    .from('snapshots')
    .remove([path])

  if (error) {
    console.error('Error deleting snapshot:', error)
    return { success: false, error }
  }

  return { success: true, error: null }
}

/**
 * Get user's snapshots
 */
export async function getUserSnapshots(userId: string) {
  const supabase = await createClient()
  
  const { data, error } = await supabase.storage
    .from('snapshots')
    .list(userId, {
      limit: 100,
      sortBy: { column: 'created_at', order: 'desc' }
    })

  if (error) {
    console.error('Error fetching snapshots:', error)
    return { data: null, error }
  }

  const snapshots = data?.map(file => ({
    name: file.name,
    path: `${userId}/${file.name}`,
    url: getPublicUrl('snapshots', `${userId}/${file.name}`),
    created_at: file.created_at
  })) || []

  return { data: snapshots, error: null }
}

/**
 * Get product image URL
 */
export function getProductImageUrl(path: string | null) {
  if (!path) return null
  return getPublicUrl('products', path)
}

/**
 * Get 3D model URL
 */
export function getModelUrl(path: string | null) {
  if (!path) return null
  return getPublicUrl('models', path)
}

/**
 * Upload product image (admin only)
 */
export async function uploadProductImage(
  file: File, 
  productSlug: string, 
  variantColor: string
) {
  const supabase = await createClient()
  
  const fileName = `${productSlug}/${variantColor}/preview.jpg`
  
  const { data, error } = await supabase.storage
    .from('products')
    .upload(fileName, file, {
      contentType: 'image/jpeg',
      upsert: true
    })

  if (error) {
    console.error('Error uploading product image:', error)
    return { data: null, error }
  }

  return { data: { path: fileName, url: getProductImageUrl(fileName) }, error: null }
}

/**
 * Upload 3D model (admin only)
 */
export async function uploadModel(
  file: File,
  productSlug: string,
  variantColor: string
) {
  const supabase = await createClient()
  
  const fileName = `${productSlug}/${variantColor}/model.glb`
  
  const { data, error } = await supabase.storage
    .from('models')
    .upload(fileName, file, {
      contentType: 'model/gltf-binary',
      upsert: true
    })

  if (error) {
    console.error('Error uploading model:', error)
    return { data: null, error }
  }

  return { data: { path: fileName, url: getModelUrl(fileName) }, error: null }
}

/**
 * Log try-on event
 */
export async function logTryOnEvent(event: {
  productId: string
  variantId?: string
  device?: string
  fps?: number
  width?: number
  height?: number
}) {
  const supabase = await createClient()
  
  const { data: { user } } = await supabase.auth.getUser()
  
  const { error } = await supabase
    .from('tryon_events')
    .insert({
      user_id: user?.id || null,
      product_id: event.productId,
      variant_id: event.variantId || null,
      device: event.device || null,
      fps: event.fps || null,
      width: event.width || null,
      height: event.height || null
    })

  if (error) {
    console.error('Error logging try-on event:', error)
    return { success: false, error }
  }

  return { success: true, error: null }
}