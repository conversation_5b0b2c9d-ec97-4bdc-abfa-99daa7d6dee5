import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface AuthState {
  user: any | null
  setUser: (user: any) => void
  clearUser: () => void
}

interface CatalogState {
  filters: {
    gender: string[]
    frameShape: string[]
    priceRange: [number, number]
    color: string[]
  }
  sorting: 'price_asc' | 'price_desc' | 'name_asc' | 'newest'
  setFilters: (filters: Partial<CatalogState['filters']>) => void
  setSorting: (sorting: CatalogState['sorting']) => void
  resetFilters: () => void
}

interface WishlistState {
  items: string[] // Product IDs
  toggleItem: (productId: string) => void
  clearWishlist: () => void
}

interface TryOnState {
  selectedProductId: string | null
  selectedVariantId: string | null
  tryOnSettings: {
    brightness: number
    zoom: number
    offsetX: number      // Horizontal position adjustment (-50 to 50)
    offsetY: number      // Vertical position adjustment (-50 to 50)
    offsetZ: number      // Depth adjustment (-20 to 20)
  }
  setSelectedProduct: (productId: string, variantId: string) => void
  setTryOnSettings: (settings: Partial<TryOnState['tryOnSettings']>) => void
  resetTryOnSettings: () => void
}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  setUser: (user) => set({ user }),
  clearUser: () => set({ user: null }),
}))

export const useCatalogStore = create<CatalogState>((set) => ({
  filters: {
    gender: [],
    frameShape: [],
    priceRange: [0, 500],
    color: [],
  },
  sorting: 'newest',
  setFilters: (filters) =>
    set((state) => ({
      filters: { ...state.filters, ...filters },
    })),
  setSorting: (sorting) => set({ sorting }),
  resetFilters: () =>
    set({
      filters: {
        gender: [],
        frameShape: [],
        priceRange: [0, 500],
        color: [],
      },
    }),
}))

export const useWishlistStore = create<WishlistState>()(
  persist(
    (set) => ({
      items: [],
      toggleItem: (productId) =>
        set((state) => ({
          items: state.items.includes(productId)
            ? state.items.filter((id) => id !== productId)
            : [...state.items, productId],
        })),
      clearWishlist: () => set({ items: [] }),
    }),
    {
      name: 'wishlist-storage',
    }
  )
)

export const useTryOnStore = create<TryOnState>((set) => ({
  selectedProductId: null,
  selectedVariantId: null,
  tryOnSettings: {
    brightness: 100,
    zoom: 1,
    offsetX: 0,
    offsetY: 0,
    offsetZ: 0,
  },
  setSelectedProduct: (productId, variantId) =>
    set({
      selectedProductId: productId,
      selectedVariantId: variantId,
    }),
  setTryOnSettings: (settings) =>
    set((state) => ({
      tryOnSettings: { ...state.tryOnSettings, ...settings },
    })),
  resetTryOnSettings: () =>
    set((state) => ({
      tryOnSettings: {
        brightness: 100,
        zoom: 1,
        offsetX: 0,
        offsetY: 0,
        offsetZ: 0,
      },
    })),
}))