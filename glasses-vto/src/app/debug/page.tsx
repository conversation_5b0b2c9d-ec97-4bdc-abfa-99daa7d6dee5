import { IntegratedDebugTest } from '@/components/debug/IntegratedDebugTest'
import { MediaPipeTest } from '@/components/debug/MediaPipeTest'
import { GlassesPositionTest } from '@/components/debug/GlassesPositionTest'

export default function DebugPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Debug Tools</h1>

      <div className="space-y-8">
        <IntegratedDebugTest />

        <details className="border rounded p-4">
          <summary className="font-semibold cursor-pointer">Individual Components (Advanced)</summary>
          <div className="mt-4 space-y-8">
            <GlassesPositionTest />
            <MediaPipeTest />
          </div>
        </details>
        
        <div className="text-sm text-gray-600">
          <h3 className="font-semibold mb-2">Debug Instructions:</h3>
          <ol className="list-decimal list-inside space-y-1">
            <li>Wait for MediaPipe to load</li>
            <li>Click "Start Camera" and allow camera access</li>
            <li>Click "Test Detection" to start face detection</li>
            <li>Position your face in the camera view</li>
            <li>Check console for detailed error messages</li>
          </ol>
          
          <h3 className="font-semibold mt-4 mb-2">Expected Behavior:</h3>
          <ul className="list-disc list-inside space-y-1">
            <li>Blue dots should appear on key face landmarks</li>
            <li>Red dots should show all face mesh points</li>
            <li>Status should show "Detecting faces: X fps, 1 face(s)"</li>
            <li>No console errors should appear</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
