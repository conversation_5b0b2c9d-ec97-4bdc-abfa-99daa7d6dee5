import { NextResponse } from 'next/server'
import { getCategories } from '@/lib/api/products'

export async function GET() {
  try {
    const result = await getCategories()

    if (result.error) {
      console.error('Error fetching categories:', result.error)
      return NextResponse.json(
        { error: 'Failed to fetch categories' },
        { status: 500 }
      )
    }

    return NextResponse.json(result.data || [])
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}