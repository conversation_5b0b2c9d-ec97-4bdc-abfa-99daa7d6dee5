import { NextRequest, NextResponse } from 'next/server'
import { 
  getUserWishlist, 
  toggleWishlist, 
  addToWishlist, 
  removeFromWishlist 
} from '@/lib/api/wishlist'

// GET user wishlist
export async function GET() {
  try {
    const result = await getUserWishlist()

    if (result.error) {
      console.error('Error fetching wishlist:', result.error)
      return NextResponse.json(
        { error: 'Failed to fetch wishlist' },
        { status: 500 }
      )
    }

    return NextResponse.json(result.data || [])
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST toggle wishlist item
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { productId, action } = body

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      )
    }

    let result

    if (action === 'add') {
      result = await addToWishlist(productId)
    } else if (action === 'remove') {
      result = await removeFromWishlist(productId)
    } else {
      result = await toggleWishlist(productId)
    }

    if (result.error) {
      // Check if it's an auth error
      if (result.error.message?.includes('not authenticated')) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }
      
      console.error('Error updating wishlist:', result.error)
      return NextResponse.json(
        { error: 'Failed to update wishlist' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE remove from wishlist
export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const productId = searchParams.get('productId')

    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      )
    }

    const result = await removeFromWishlist(productId)

    if (result.error) {
      console.error('Error removing from wishlist:', result.error)
      return NextResponse.json(
        { error: 'Failed to remove from wishlist' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}