import { NextRequest, NextResponse } from 'next/server'
import { getCatalog, getFeaturedProducts } from '@/lib/api/products'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const search = searchParams.get('search') || undefined
    const category = searchParams.get('category') || undefined
    const color = searchParams.get('color') || undefined
    const priceMin = searchParams.get('priceMin') ? parseInt(searchParams.get('priceMin')!) : undefined
    const priceMax = searchParams.get('priceMax') ? parseInt(searchParams.get('priceMax')!) : undefined
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 24
    const offset = searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0
    const featured = searchParams.get('featured') === 'true'

    let result

    if (featured) {
      result = await getFeaturedProducts()
    } else {
      result = await getCatalog({
        search,
        categorySlug: category,
        color,
        priceMin,
        priceMax,
        limit,
        offset
      })
    }

    if (result.error) {
      console.error('Error fetching products:', result.error)
      return NextResponse.json(
        { error: 'Failed to fetch products' },
        { status: 500 }
      )
    }

    // Transform data to match frontend expectations
    const products = result.data || []
    
    // For the catalog, we need to also fetch variants for each product
    if (!featured && products.length > 0) {
      const { createClient } = await import('@/lib/supabase/server')
      const supabase = await createClient()
      
      const productIds = products.map((p: any) => p.id)
      const { data: variants } = await supabase
        .from('product_variants')
        .select('*')
        .in('product_id', productIds)
        .eq('active', true)
      
      // Attach variants to products
      const productsWithVariants = products.map((product: any) => ({
        ...product,
        product_variants: variants?.filter((v: any) => v.product_id === product.id) || []
      }))
      
      return NextResponse.json(productsWithVariants)
    }

    return NextResponse.json(products)
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}