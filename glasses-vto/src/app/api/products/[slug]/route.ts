import { NextRequest, NextResponse } from 'next/server'
import { getProductDetails } from '@/lib/api/products'
import { getProductImageUrl, getModelUrl } from '@/lib/api/storage'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params

    if (!slug) {
      return NextResponse.json(
        { error: 'Product slug is required' },
        { status: 400 }
      )
    }

    const result = await getProductDetails(slug)

    if (result.error || !result.data) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }

    // Transform variants to include full URLs
    const product = result.data
    if (product.variants) {
      product.variants = product.variants.map(variant => ({
        ...variant,
        preview_image_url: getProductImageUrl(variant.preview_image_path),
        model_url: getModelUrl(variant.glb_path)
      }))
    }

    return NextResponse.json(product)
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}