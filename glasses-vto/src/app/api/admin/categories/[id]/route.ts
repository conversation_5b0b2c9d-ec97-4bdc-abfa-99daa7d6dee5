import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createAdminClient } from '@/lib/supabase/admin'
import type { Database } from '@/types/database'

type CategoryRow = Database['public']['Tables']['categories']['Row']
type CategoryWithCount = CategoryRow & {
  product_categories?: { count: number }[]
}

function unauthorizedResponse() {
  return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createClient()

    const {
      data: { user }
    } = await supabase.auth.getUser()

    if (!user) {
      return unauthorizedResponse()
    }

    const { data, error } = await supabase
      .from('categories')
      .select('*, product_categories(count)')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching category:', error)
      return NextResponse.json({ error: 'Category not found' }, { status: 404 })
    }

    const { product_categories, ...category } = (data ?? {}) as CategoryWithCount

    return NextResponse.json({
      ...category,
      _count: {
        products: product_categories?.[0]?.count ?? 0
      }
    })
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createClient()

    const {
      data: { user }
    } = await supabase.auth.getUser()

    if (!user) {
      return unauthorizedResponse()
    }

    let adminClient

    try {
      adminClient = createAdminClient()
    } catch (error) {
      console.error('Admin client error:', error)
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 })
    }

    const body = await request.json()
    const updates: Record<string, string> = {}

    if (typeof body?.name === 'string' && body.name.trim()) {
      updates.name = body.name.trim()
    }

    if (typeof body?.slug === 'string' && body.slug.trim()) {
      updates.slug = body.slug.trim().toLowerCase()
    }

    if (Object.keys(updates).length === 0) {
      return NextResponse.json({ error: 'No valid fields to update' }, { status: 400 })
    }

    const { data, error } = await adminClient
      .from('categories')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating category:', error)
      return NextResponse.json({ error: 'Failed to update category' }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const supabase = await createClient()

    const {
      data: { user }
    } = await supabase.auth.getUser()

    if (!user) {
      return unauthorizedResponse()
    }

    let adminClient

    try {
      adminClient = createAdminClient()
    } catch (error) {
      console.error('Admin client error:', error)
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 })
    }

    const { error: relationError } = await adminClient
      .from('product_categories')
      .delete()
      .eq('category_id', id)

    if (relationError) {
      console.error('Error removing category relations:', relationError)
      return NextResponse.json({ error: 'Failed to remove category relations' }, { status: 500 })
    }

    const { error } = await adminClient
      .from('categories')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting category:', error)
      return NextResponse.json({ error: 'Failed to delete category' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
