import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createAdminClient } from '@/lib/supabase/admin'
import type { Database } from '@/types/database'

type CategoryRow = Database['public']['Tables']['categories']['Row']
type CategoryWithCount = CategoryRow & {
  product_categories?: { count: number }[]
}

function unauthorizedResponse() {
  return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
}

export async function GET() {
  try {
    const supabase = await createClient()

    const {
      data: { user }
    } = await supabase.auth.getUser()

    if (!user) {
      return unauthorizedResponse()
    }

    const { data, error } = await supabase
      .from('categories')
      .select('*, product_categories(count)')
      .order('name')

    if (error) {
      console.error('Error fetching categories:', error)
      return NextResponse.json({ error: 'Failed to fetch categories' }, { status: 500 })
    }

    const rawCategories = (data ?? []) as CategoryWithCount[]
    const categories = rawCategories.map(({ product_categories, ...rest }) => ({
      ...rest,
      _count: {
        products: product_categories?.[0]?.count ?? 0
      }
    }))

    return NextResponse.json(categories)
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    const {
      data: { user }
    } = await supabase.auth.getUser()

    if (!user) {
      return unauthorizedResponse()
    }

    let adminClient

    try {
      adminClient = createAdminClient()
    } catch (error) {
      console.error('Admin client error:', error)
      return NextResponse.json({ error: 'Server configuration error' }, { status: 500 })
    }

    const body = await request.json()
    const name = typeof body?.name === 'string' ? body.name.trim() : ''
    const slug = typeof body?.slug === 'string' ? body.slug.trim().toLowerCase() : ''

    if (!name || !slug) {
      return NextResponse.json({ error: 'Name and slug are required' }, { status: 400 })
    }

    const { data, error } = await adminClient
      .from('categories')
      .insert({ name, slug })
      .select()
      .single()

    if (error) {
      console.error('Error creating category:', error)
      if ('code' in error && error.code === '23505') {
        return NextResponse.json({ error: 'Slug already exists' }, { status: 409 })
      }

      return NextResponse.json({ error: 'Failed to create category' }, { status: 500 })
    }

    return NextResponse.json(data, { status: 201 })
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
