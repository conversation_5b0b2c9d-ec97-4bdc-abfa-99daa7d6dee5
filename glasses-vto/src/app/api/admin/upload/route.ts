import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createAdminClient } from '@/lib/supabase/admin'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const bucket = formData.get('bucket') as string
    const path = formData.get('path') as string

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    if (!bucket || !path) {
      return NextResponse.json({ error: 'Bucket and path are required' }, { status: 400 })
    }

    // Validate file type based on bucket
    if (bucket === 'models') {
      const allowedTypes = ['model/gltf-binary', 'application/octet-stream']
      const allowedExtensions = ['.glb', '.gltf']
      
      const hasValidType = allowedTypes.includes(file.type)
      const hasValidExtension = allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext))
      
      if (!hasValidType && !hasValidExtension) {
        return NextResponse.json({ 
          error: 'Invalid file type. Only GLB and GLTF files are allowed for models.' 
        }, { status: 400 })
      }
    } else if (bucket === 'products') {
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
      
      if (!allowedTypes.includes(file.type)) {
        return NextResponse.json({ 
          error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed for products.' 
        }, { status: 400 })
      }
    }

    // Validate file size (max 10MB for models, 5MB for images)
    const maxSize = bucket === 'models' ? 10 * 1024 * 1024 : 5 * 1024 * 1024
    if (file.size > maxSize) {
      const maxSizeMB = maxSize / (1024 * 1024)
      return NextResponse.json({ 
        error: `File too large. Maximum size is ${maxSizeMB}MB.` 
      }, { status: 400 })
    }

    // Convert file to buffer
    const buffer = await file.arrayBuffer()
    const uint8Array = new Uint8Array(buffer)

    // Use admin client to upload file (bypasses RLS)
    const adminSupabase = createAdminClient()
    
    const { data, error } = await adminSupabase.storage
      .from(bucket)
      .upload(path, uint8Array, {
        contentType: file.type,
        upsert: true // Allow overwriting existing files
      })

    if (error) {
      console.error('Storage upload error:', error)
      return NextResponse.json({ error: 'Failed to upload file' }, { status: 500 })
    }

    // Get public URL
    const { data: { publicUrl } } = adminSupabase.storage
      .from(bucket)
      .getPublicUrl(path)

    return NextResponse.json({
      path: data.path,
      publicUrl,
      size: file.size,
      type: file.type,
      name: file.name
    })

  } catch (error) {
    console.error('Upload API Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// GET - List files in a bucket/path
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const bucket = searchParams.get('bucket')
    const path = searchParams.get('path') || ''

    if (!bucket) {
      return NextResponse.json({ error: 'Bucket is required' }, { status: 400 })
    }

    const adminSupabase = createAdminClient()
    
    const { data, error } = await adminSupabase.storage
      .from(bucket)
      .list(path)

    if (error) {
      console.error('Storage list error:', error)
      return NextResponse.json({ error: 'Failed to list files' }, { status: 500 })
    }

    // Add public URLs to each file
    const filesWithUrls = data.map(file => {
      const { data: { publicUrl } } = adminSupabase.storage
        .from(bucket)
        .getPublicUrl(path ? `${path}/${file.name}` : file.name)
      
      return {
        ...file,
        publicUrl
      }
    })

    return NextResponse.json(filesWithUrls)

  } catch (error) {
    console.error('List API Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE - Remove a file
export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const bucket = searchParams.get('bucket')
    const path = searchParams.get('path')

    if (!bucket || !path) {
      return NextResponse.json({ error: 'Bucket and path are required' }, { status: 400 })
    }

    const adminSupabase = createAdminClient()
    
    const { error } = await adminSupabase.storage
      .from(bucket)
      .remove([path])

    if (error) {
      console.error('Storage delete error:', error)
      return NextResponse.json({ error: 'Failed to delete file' }, { status: 500 })
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Delete API Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
