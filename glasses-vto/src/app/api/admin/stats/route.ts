import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch various stats
    const [
      productsResult,
      categoriesResult,
      usersResult,
      tryOnResult
    ] = await Promise.all([
      // Total and active products
      supabase
        .from('products')
        .select('active', { count: 'exact' }),
      
      // Total categories
      supabase
        .from('categories')
        .select('*', { count: 'exact', head: true }),
      
      // Total users (if we have access)
      supabase.auth.admin?.listUsers(),
      
      // Recent try-ons (last 7 days)
      supabase
        .from('tryon_events')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
    ])

    const totalProducts = productsResult.count || 0
    const activeProducts = productsResult.data?.filter(p => p.active).length || 0
    const totalCategories = categoriesResult.count || 0
    const totalUsers = usersResult?.data?.users?.length || 0
    const recentTryOns = tryOnResult.count || 0

    // For media files, we'd need to count storage items
    // This is a placeholder - you might need to implement a different approach
    const totalMedia = 0

    return NextResponse.json({
      totalProducts,
      activeProducts,
      totalCategories,
      totalUsers,
      totalMedia,
      recentTryOns
    })
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}