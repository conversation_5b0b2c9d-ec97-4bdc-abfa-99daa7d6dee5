import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createAdminClient } from '@/lib/supabase/admin'

// GET all products for admin
export async function GET() {
  try {
    const supabase = await createClient()
    
    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        product_variants (count)
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching products:', error)
      return NextResponse.json({ error: 'Failed to fetch products' }, { status: 500 })
    }

    // Transform the data to include variant count
    const products = data?.map(product => ({
      ...product,
      _count: {
        variants: product.product_variants?.[0]?.count || 0
      }
    }))

    return NextResponse.json(products)
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST create new product
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { categories, ...productData } = body

    // Use admin client to bypass RLS
    const adminSupabase = createAdminClient()

    // Insert product
    const { data: product, error: productError } = await adminSupabase
      .from('products')
      .insert(productData)
      .select()
      .single()

    if (productError) {
      console.error('Error creating product:', productError)
      return NextResponse.json({ error: 'Failed to create product' }, { status: 500 })
    }

    // Insert product categories
    if (categories && categories.length > 0) {
      const categoryRelations = categories.map((categoryId: string) => ({
        product_id: product.id,
        category_id: categoryId
      }))

      const { error: categoryError } = await adminSupabase
        .from('product_categories')
        .insert(categoryRelations)

      if (categoryError) {
        console.error('Error adding categories:', categoryError)
      }
    }

    return NextResponse.json(product)
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}