'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import {
  ArrowLeft,
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  Upload,
  Eye,
  Loader2
} from 'lucide-react'
import FileUpload from '@/components/admin/FileUpload'

interface Product {
  id: string
  name: string
  slug: string
  brand: string | null
  price_cents: number
}

interface ProductVariant {
  id: string
  product_id: string
  sku: string | null
  color: string | null
  size: string | null
  bridge_width_mm: number | null
  temple_length_mm: number | null
  lens_width_mm: number | null
  stock: number
  glb_path: string | null
  preview_image_path: string | null
  active: boolean
  created_at: string
}

const initialVariantForm = {
  sku: '',
  color: '',
  size: '',
  bridge_width_mm: '',
  temple_length_mm: '',
  lens_width_mm: '',
  stock: '0',
  glb_path: '',
  preview_image_path: ''
}

export default function ProductVariantsPage() {
  const params = useParams()
  const router = useRouter()
  const productId = params.id as string
  
  const [product, setProduct] = useState<Product | null>(null)
  const [variants, setVariants] = useState<ProductVariant[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [formData, setFormData] = useState(initialVariantForm)
  const [saving, setSaving] = useState(false)
  const [editingVariant, setEditingVariant] = useState<ProductVariant | null>(null)

  useEffect(() => {
    fetchProduct()
    fetchVariants()
  }, [productId])

  async function fetchProduct() {
    try {
      const response = await fetch(`/api/admin/products/${productId}`)
      if (response.ok) {
        const data = await response.json()
        setProduct(data)
      }
    } catch (error) {
      console.error('Error fetching product:', error)
    }
  }

  async function fetchVariants() {
    try {
      const response = await fetch(`/api/admin/products/${productId}/variants`)
      if (response.ok) {
        const data = await response.json()
        setVariants(data)
      }
    } catch (error) {
      console.error('Error fetching variants:', error)
    } finally {
      setLoading(false)
    }
  }

  function openCreateDialog() {
    setEditingVariant(null)
    setFormData(initialVariantForm)
    setDialogOpen(true)
  }

  function openEditDialog(variant: ProductVariant) {
    setEditingVariant(variant)
    setFormData({
      sku: variant.sku || '',
      color: variant.color || '',
      size: variant.size || '',
      bridge_width_mm: variant.bridge_width_mm?.toString() || '',
      temple_length_mm: variant.temple_length_mm?.toString() || '',
      lens_width_mm: variant.lens_width_mm?.toString() || '',
      stock: variant.stock.toString(),
      glb_path: variant.glb_path || '',
      preview_image_path: variant.preview_image_path || ''
    })
    setDialogOpen(true)
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault()
    setSaving(true)

    try {
      const variantData = {
        product_id: productId,
        sku: formData.sku || null,
        color: formData.color || null,
        size: formData.size || null,
        bridge_width_mm: formData.bridge_width_mm ? parseInt(formData.bridge_width_mm) : null,
        temple_length_mm: formData.temple_length_mm ? parseInt(formData.temple_length_mm) : null,
        lens_width_mm: formData.lens_width_mm ? parseInt(formData.lens_width_mm) : null,
        stock: parseInt(formData.stock) || 0,
        glb_path: formData.glb_path || null,
        preview_image_path: formData.preview_image_path || null
      }

      const url = editingVariant 
        ? `/api/admin/products/${productId}/variants/${editingVariant.id}`
        : `/api/admin/products/${productId}/variants`
      
      const method = editingVariant ? 'PATCH' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(variantData),
      })

      if (response.ok) {
        await fetchVariants()
        setDialogOpen(false)
        setFormData(initialVariantForm)
        setEditingVariant(null)
      } else {
        const error = await response.text()
        alert(`Failed to ${editingVariant ? 'update' : 'create'} variant: ${error}`)
      }
    } catch (error) {
      console.error('Error saving variant:', error)
      alert('Error saving variant')
    } finally {
      setSaving(false)
    }
  }

  async function deleteVariant(variantId: string) {
    if (!confirm('Are you sure you want to delete this variant?')) return

    try {
      const response = await fetch(`/api/admin/products/${productId}/variants/${variantId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        await fetchVariants()
      } else {
        const error = await response.text()
        alert(`Failed to delete variant: ${error}`)
      }
    } catch (error) {
      console.error('Error deleting variant:', error)
      alert('Error deleting variant')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center space-x-4 mb-4">
          <Link href="/admin/products">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Product Variants</h1>
            <p className="text-gray-600 mt-1">
              {product?.name} - Manage colors, sizes, and 3D models
            </p>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href={`/admin/products/${productId}/edit`}>
              <Button variant="outline">
                <Edit className="h-4 w-4 mr-2" />
                Edit Product
              </Button>
            </Link>
          </div>
          <Button onClick={openCreateDialog}>
            <Plus className="h-4 w-4 mr-2" />
            Add Variant
          </Button>
        </div>
      </div>

      {/* Variants Table */}
      <Card>
        <CardHeader>
          <CardTitle>Variants ({variants.length})</CardTitle>
          <CardDescription>
            Different color and size combinations for this product
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>SKU</TableHead>
                <TableHead>Color</TableHead>
                <TableHead>Size</TableHead>
                <TableHead>Dimensions</TableHead>
                <TableHead>Stock</TableHead>
                <TableHead>3D Model</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {variants.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                    No variants found. Add your first variant to get started.
                  </TableCell>
                </TableRow>
              ) : (
                variants.map((variant) => (
                  <TableRow key={variant.id}>
                    <TableCell className="font-mono text-sm">
                      {variant.sku || '-'}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <div 
                          className="w-4 h-4 rounded-full border"
                          style={{ backgroundColor: variant.color || '#ccc' }}
                        />
                        <span>{variant.color || '-'}</span>
                      </div>
                    </TableCell>
                    <TableCell>{variant.size || '-'}</TableCell>
                    <TableCell className="text-sm">
                      {variant.bridge_width_mm || variant.temple_length_mm || variant.lens_width_mm ? (
                        <div>
                          {variant.bridge_width_mm && `B:${variant.bridge_width_mm}mm `}
                          {variant.temple_length_mm && `T:${variant.temple_length_mm}mm `}
                          {variant.lens_width_mm && `L:${variant.lens_width_mm}mm`}
                        </div>
                      ) : '-'}
                    </TableCell>
                    <TableCell>
                      <Badge variant={variant.stock > 0 ? 'default' : 'secondary'}>
                        {variant.stock} in stock
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {variant.glb_path ? (
                        <Badge variant="outline" className="text-green-600">
                          <Eye className="h-3 w-3 mr-1" />
                          GLB
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-gray-400">
                          No 3D Model
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant={variant.active ? 'default' : 'secondary'}>
                        {variant.active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => openEditDialog(variant)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            className="text-red-600"
                            onClick={() => deleteVariant(variant.id)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add/Edit Variant Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingVariant ? 'Edit Variant' : 'Add New Variant'}
            </DialogTitle>
            <DialogDescription>
              Configure the variant details including dimensions and file paths
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-2 gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="sku">SKU</Label>
                <Input
                  id="sku"
                  value={formData.sku}
                  onChange={(e) => setFormData({ ...formData, sku: e.target.value })}
                  placeholder="e.g. AVT-BLK-M"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="color">Color</Label>
                <Input
                  id="color"
                  value={formData.color}
                  onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                  placeholder="e.g. Black, Tortoise"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="size">Size</Label>
                <Input
                  id="size"
                  value={formData.size}
                  onChange={(e) => setFormData({ ...formData, size: e.target.value })}
                  placeholder="e.g. S, M, L"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stock">Stock</Label>
                <Input
                  id="stock"
                  type="number"
                  value={formData.stock}
                  onChange={(e) => setFormData({ ...formData, stock: e.target.value })}
                  min="0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="bridge_width_mm">Bridge Width (mm)</Label>
                <Input
                  id="bridge_width_mm"
                  type="number"
                  value={formData.bridge_width_mm}
                  onChange={(e) => setFormData({ ...formData, bridge_width_mm: e.target.value })}
                  placeholder="e.g. 18"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="temple_length_mm">Temple Length (mm)</Label>
                <Input
                  id="temple_length_mm"
                  type="number"
                  value={formData.temple_length_mm}
                  onChange={(e) => setFormData({ ...formData, temple_length_mm: e.target.value })}
                  placeholder="e.g. 140"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lens_width_mm">Lens Width (mm)</Label>
                <Input
                  id="lens_width_mm"
                  type="number"
                  value={formData.lens_width_mm}
                  onChange={(e) => setFormData({ ...formData, lens_width_mm: e.target.value })}
                  placeholder="e.g. 52"
                />
              </div>

              <div className="col-span-2 space-y-2">
                <Label>3D Model Upload (GLB)</Label>
                <FileUpload
                  bucket="models"
                  path={product ? `${product.slug}/${formData.color || 'default'}/model.glb` : undefined}
                  onUploadComplete={(result) => {
                    setFormData({ ...formData, glb_path: result.path })
                  }}
                  onUploadError={(error) => {
                    console.error('GLB upload error:', error)
                  }}
                  disabled={saving}
                />
                <Input
                  placeholder="Or enter path manually"
                  value={formData.glb_path}
                  onChange={(e) => setFormData({ ...formData, glb_path: e.target.value })}
                  className="text-sm"
                />
              </div>

              <div className="col-span-2 space-y-2">
                <Label>Preview Image Upload</Label>
                <FileUpload
                  bucket="products"
                  path={product ? `${product.slug}/${formData.color || 'default'}/preview.jpg` : undefined}
                  onUploadComplete={(result) => {
                    setFormData({ ...formData, preview_image_path: result.path })
                  }}
                  onUploadError={(error) => {
                    console.error('Image upload error:', error)
                  }}
                  disabled={saving}
                />
                <Input
                  placeholder="Or enter path manually"
                  value={formData.preview_image_path}
                  onChange={(e) => setFormData({ ...formData, preview_image_path: e.target.value })}
                  className="text-sm"
                />
              </div>
            </div>

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setDialogOpen(false)}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={saving}>
                {saving ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {editingVariant ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  editingVariant ? 'Update Variant' : 'Create Variant'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
