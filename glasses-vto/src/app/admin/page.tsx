'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Package, Tag, Users, Image, TrendingUp, Eye } from 'lucide-react'

interface DashboardStats {
  totalProducts: number
  totalCategories: number
  totalUsers: number
  totalMedia: number
  recentTryOns: number
  activeProducts: number
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalCategories: 0,
    totalUsers: 0,
    totalMedia: 0,
    recentTryOns: 0,
    activeProducts: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  async function fetchStats() {
    try {
      // Fetch stats from API
      const response = await fetch('/api/admin/stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const statCards = [
    {
      title: 'Total Products',
      value: stats.totalProducts,
      icon: Package,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Active Products',
      value: stats.activeProducts,
      icon: Eye,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'Categories',
      value: stats.totalCategories,
      icon: Tag,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Total Users',
      value: stats.totalUsers,
      icon: Users,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
    {
      title: 'Media Files',
      value: stats.totalMedia,
      icon: Image,
      color: 'text-pink-600',
      bgColor: 'bg-pink-100'
    },
    {
      title: 'Recent Try-Ons',
      value: stats.recentTryOns,
      icon: TrendingUp,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100'
    }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">Welcome to Glass VTO Admin Panel</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {statCards.map((stat) => {
          const Icon = stat.icon
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <a
              href="/admin/products/new"
              className="p-4 text-center border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Package className="h-8 w-8 mx-auto mb-2 text-primary" />
              <span className="text-sm font-medium">Add Product</span>
            </a>
            <a
              href="/admin/categories"
              className="p-4 text-center border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Tag className="h-8 w-8 mx-auto mb-2 text-primary" />
              <span className="text-sm font-medium">Manage Categories</span>
            </a>
            <a
              href="/admin/media"
              className="p-4 text-center border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Image className="h-8 w-8 mx-auto mb-2 text-primary" />
              <span className="text-sm font-medium">Upload Media</span>
            </a>
            <a
              href="/admin/users"
              className="p-4 text-center border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Users className="h-8 w-8 mx-auto mb-2 text-primary" />
              <span className="text-sm font-medium">View Users</span>
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}