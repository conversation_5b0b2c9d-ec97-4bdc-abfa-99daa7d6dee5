'use client'

import { useEffect, useMemo, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import {
  Edit,
  MoreHorizontal,
  Plus,
  Search,
  Tag,
  Trash2
} from 'lucide-react'

interface Category {
  id: string
  name: string
  slug: string
  _count?: {
    products: number
  }
}

type FormMode = 'create' | 'edit'

const initialFormState = {
  name: '',
  slug: ''
}

function generateSlug(value: string) {
  return value
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '')
}

function sortCategories(list: Category[]) {
  return [...list].sort((a, b) => a.name.localeCompare(b.name))
}

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [dialogOpen, setDialogOpen] = useState(false)
  const [formMode, setFormMode] = useState<FormMode>('create')
  const [formData, setFormData] = useState(initialFormState)
  const [saving, setSaving] = useState(false)
  const [activeCategory, setActiveCategory] = useState<Category | null>(null)
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null)

  useEffect(() => {
    fetchCategories()
  }, [])

  async function fetchCategories() {
    try {
      const response = await fetch('/api/admin/categories')
      if (response.ok) {
        const data = await response.json()
        setCategories(sortCategories(data as Category[]))
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    } finally {
      setLoading(false)
    }
  }

  function openCreateDialog() {
    setFormMode('create')
    setFormData(initialFormState)
    setActiveCategory(null)
    setDialogOpen(true)
  }

  function openEditDialog(category: Category) {
    setFormMode('edit')
    setActiveCategory(category)
    setFormData({
      name: category.name,
      slug: category.slug
    })
    setDialogOpen(true)
  }

  function closeDialog() {
    setDialogOpen(false)
    setFormData(initialFormState)
    setActiveCategory(null)
    setSaving(false)
  }

  function handleNameChange(value: string) {
    setFormData(prev => ({
      ...prev,
      name: value,
      slug:
        formMode === 'create' || prev.slug.length === 0
          ? generateSlug(value)
          : prev.slug
    }))
  }

  function handleSlugChange(value: string) {
    setFormData(prev => ({
      ...prev,
      slug: generateSlug(value)
    }))
  }

  async function handleSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault()

    const trimmedName = formData.name.trim()
    const trimmedSlug = formData.slug.trim()

    if (!trimmedName || !trimmedSlug) {
      alert('Name and slug are required')
      return
    }

    setSaving(true)

    try {
      if (formMode === 'create') {
        const response = await fetch('/api/admin/categories', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            name: trimmedName,
            slug: trimmedSlug
          })
        })

        if (!response.ok) {
          const errorPayload = await response.json().catch(() => null)
          throw new Error(errorPayload?.error || 'Failed to create category')
        }

        const category = await response.json()
        setCategories(prev =>
          sortCategories([
            ...prev,
            {
              ...category,
              _count: { products: 0 }
            }
          ])
        )
      } else if (activeCategory) {
        const response = await fetch(`/api/admin/categories/${activeCategory.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            name: trimmedName,
            slug: trimmedSlug
          })
        })

        if (!response.ok) {
          const errorPayload = await response.json().catch(() => null)
          throw new Error(errorPayload?.error || 'Failed to update category')
        }

        const updatedCategory = await response.json()
        setCategories(prev =>
          sortCategories(prev.map(category =>
            category.id === activeCategory.id
              ? {
                  ...category,
                  ...updatedCategory
                }
              : category
          ))
        )
      }

      closeDialog()
    } catch (error) {
      console.error('Error saving category:', error)
      alert(error instanceof Error ? error.message : 'Failed to save category')
      setSaving(false)
    }
  }

  async function deleteCategory(id: string) {
    if (!confirm('Are you sure you want to delete this category?')) {
      return
    }

    setDeleteLoading(id)

    try {
      const response = await fetch(`/api/admin/categories/${id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorPayload = await response.json().catch(() => null)
        throw new Error(errorPayload?.error || 'Failed to delete category')
      }

      setCategories(prev => prev.filter(category => category.id !== id))
    } catch (error) {
      console.error('Error deleting category:', error)
      alert(error instanceof Error ? error.message : 'Failed to delete category')
    } finally {
      setDeleteLoading(null)
    }
  }

  const filteredCategories = useMemo(
    () =>
      categories.filter(category => {
        const query = searchQuery.toLowerCase()
        return (
          category.name.toLowerCase().includes(query) ||
          category.slug.toLowerCase().includes(query)
        )
      }),
    [categories, searchQuery]
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Categories</h1>
          <p className="mt-2 text-gray-600">
            Manage your product taxonomy and groupings
          </p>
        </div>
        <Button onClick={openCreateDialog}>
          <Plus className="mr-2 h-4 w-4" />
          Add Category
        </Button>
      </div>

      <div className="mb-6">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
          <Input
            type="text"
            placeholder="Search categories..."
            value={searchQuery}
            onChange={event => setSearchQuery(event.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <div className="rounded-lg bg-white shadow">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Slug</TableHead>
              <TableHead>Products</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCategories.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="py-8 text-center text-gray-500">
                  {searchQuery
                    ? 'No categories match your search'
                    : 'No categories found'}
                </TableCell>
              </TableRow>
            ) : (
              filteredCategories.map(category => (
                <TableRow key={category.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <span className="flex h-9 w-9 items-center justify-center rounded-full bg-primary/10 text-primary">
                        <Tag className="h-4 w-4" />
                      </span>
                      <div>
                        <p className="font-medium text-gray-900">{category.name}</p>
                        <p className="text-sm text-gray-500">ID: {category.id}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{category.slug}</TableCell>
                  <TableCell>{category._count?.products ?? 0}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onSelect={() => openEditDialog(category)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-red-600 focus:text-red-600"
                          onSelect={() => deleteCategory(category.id)}
                          disabled={deleteLoading === category.id}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          {deleteLoading === category.id ? 'Deleting...' : 'Delete'}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <Dialog open={dialogOpen} onOpenChange={open => (open ? setDialogOpen(true) : closeDialog())}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {formMode === 'create' ? 'Create Category' : 'Edit Category'}
            </DialogTitle>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="category-name">Name</Label>
              <Input
                id="category-name"
                placeholder="e.g. Sunglasses"
                value={formData.name}
                onChange={event => handleNameChange(event.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category-slug">Slug</Label>
              <Input
                id="category-slug"
                placeholder="sunglasses"
                value={formData.slug}
                onChange={event => handleSlugChange(event.target.value)}
                required
              />
              <p className="text-sm text-gray-500">
                Used in URLs. Lowercase letters, numbers, and dashes only.
              </p>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={closeDialog}>
                Cancel
              </Button>
              <Button type="submit" disabled={saving}>
                {saving
                  ? formMode === 'create'
                    ? 'Creating...'
                    : 'Saving...'
                  : formMode === 'create'
                  ? 'Create Category'
                  : 'Save Changes'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
