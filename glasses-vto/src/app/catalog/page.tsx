'use client'

import { useEffect, useState } from 'react'
import { ProductCard } from '@/components/catalog/product-card'
import { ProductFilters } from '@/components/catalog/product-filters'
import { Button } from '@/components/ui/button'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import { Filter, SlidersHorizontal } from 'lucide-react'
import { getProductImageUrl } from '@/lib/api/storage-client'

interface Product {
  id: string
  slug: string
  name: string
  description: string | null
  brand: string | null
  price_cents: number
  active: boolean
  created_at: string
  updated_at: string
  product_variants?: any[]
}

function CatalogContent() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchProducts() {
      try {
        const response = await fetch('/api/products')
        if (!response.ok) {
          throw new Error('Failed to fetch products')
        }
        const data = await response.json()
        setProducts(data)
      } catch (err) {
        console.error('Error fetching products:', err)
        setError(err instanceof Error ? err.message : 'Failed to load products')
      } finally {
        setLoading(false)
      }
    }

    fetchProducts()
  }, [])

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-500">Loading products...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <p className="text-red-500">Error: {error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Our Collection</h1>
        <p className="text-gray-600">
          Discover {products.length} premium frames designed for every style
        </p>
      </div>

      <div className="flex gap-8">
        {/* Desktop Filters - Sidebar */}
        <aside className="hidden lg:block w-64 flex-shrink-0">
          <div className="sticky top-20">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <SlidersHorizontal className="h-5 w-5 mr-2" />
              Filters
            </h2>
            <ProductFilters />
          </div>
        </aside>

        {/* Products Grid */}
        <div className="flex-1">
          {/* Mobile Filter Button */}
          <div className="lg:hidden mb-4 flex justify-between items-center">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" className="gap-2">
                  <Filter className="h-4 w-4" />
                  Filters
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-80">
                <div className="mt-6">
                  <h2 className="text-lg font-semibold mb-4">Filters</h2>
                  <ProductFilters />
                </div>
              </SheetContent>
            </Sheet>
            
            <p className="text-sm text-gray-600">{products.length} products</p>
          </div>

          {/* Products Grid */}
          {products.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {products.map((product) => (
                <ProductCard 
                  key={product.id} 
                  product={{
                    ...product,
                    price: product.price_cents / 100, // Convert cents to dollars
                    variants: product.product_variants,
                    // Add preview image from first variant if exists
                    preview_image: product.product_variants?.[0]?.preview_image_path 
                      ? getProductImageUrl(product.product_variants[0].preview_image_path) 
                      : null
                  }} 
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">No products found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default function CatalogPage() {
  return <CatalogContent />
}
