import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ArrowRight, Camera, Sparkles, Shield, Zap } from "lucide-react";

export default function Home() {
  return (
    <div className="">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-b from-blue-50 to-white py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="mb-6 text-5xl font-bold tracking-tight sm:text-6xl lg:text-7xl">
              Find Your Perfect Frames
              <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Try Before You Buy
              </span>
            </h1>
            <p className="mb-10 text-xl text-gray-600">
              Experience glasses virtually with our advanced AR technology.
              See how frames look on you instantly using your camera.
            </p>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button size="lg" className="text-lg" asChild>
                <Link href="/catalog">
                  Browse Catalog
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="text-lg" asChild>
                <Link href="/catalog?try-on=true">
                  <Camera className="mr-2 h-5 w-5" />
                  Try Virtual Try-On
                </Link>
              </Button>
            </div>
          </div>
        </div>
        {/* Decorative Elements */}
        <div className="absolute -top-10 -right-10 h-40 w-40 rounded-full bg-purple-200 opacity-20 blur-3xl" />
        <div className="absolute -bottom-10 -left-10 h-40 w-40 rounded-full bg-blue-200 opacity-20 blur-3xl" />
      </section>

      {/* Features Section */}
      <section className="py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mb-12 text-center">
            <h2 className="mb-4 text-3xl font-bold sm:text-4xl">
              Why Choose GlassVTO?
            </h2>
            <p className="text-lg text-gray-600">
              Advanced technology meets premium eyewear
            </p>
          </div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            <Card className="p-6 text-center hover:shadow-lg transition-shadow">
              <div className="mb-4 flex justify-center">
                <div className="rounded-full bg-blue-100 p-3">
                  <Camera className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <h3 className="mb-2 font-semibold">Real-Time Try-On</h3>
              <p className="text-sm text-gray-600">
                See how frames look on your face instantly using your device camera
              </p>
            </Card>
            <Card className="p-6 text-center hover:shadow-lg transition-shadow">
              <div className="mb-4 flex justify-center">
                <div className="rounded-full bg-purple-100 p-3">
                  <Sparkles className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <h3 className="mb-2 font-semibold">AI-Powered Fit</h3>
              <p className="text-sm text-gray-600">
                Advanced face tracking ensures accurate frame positioning
              </p>
            </Card>
            <Card className="p-6 text-center hover:shadow-lg transition-shadow">
              <div className="mb-4 flex justify-center">
                <div className="rounded-full bg-green-100 p-3">
                  <Zap className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <h3 className="mb-2 font-semibold">Instant Switch</h3>
              <p className="text-sm text-gray-600">
                Try multiple frames quickly with seamless variant switching
              </p>
            </Card>
            <Card className="p-6 text-center hover:shadow-lg transition-shadow">
              <div className="mb-4 flex justify-center">
                <div className="rounded-full bg-orange-100 p-3">
                  <Shield className="h-6 w-6 text-orange-600" />
                </div>
              </div>
              <h3 className="mb-2 font-semibold">Privacy First</h3>
              <p className="text-sm text-gray-600">
                Your camera data stays on your device, ensuring complete privacy
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="bg-gray-50 py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="mb-12 text-center">
            <h2 className="mb-4 text-3xl font-bold sm:text-4xl">How It Works</h2>
            <p className="text-lg text-gray-600">
              Three simple steps to find your perfect frames
            </p>
          </div>
          <div className="mx-auto max-w-4xl">
            <div className="grid gap-12 md:grid-cols-3">
              <div className="text-center">
                <div className="mb-4 flex justify-center">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white text-lg font-bold">
                    1
                  </div>
                </div>
                <h3 className="mb-2 text-lg font-semibold">Browse Catalog</h3>
                <p className="text-sm text-gray-600">
                  Explore our curated collection of premium frames
                </p>
              </div>
              <div className="text-center">
                <div className="mb-4 flex justify-center">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white text-lg font-bold">
                    2
                  </div>
                </div>
                <h3 className="mb-2 text-lg font-semibold">Enable Camera</h3>
                <p className="text-sm text-gray-600">
                  Allow camera access for the virtual try-on experience
                </p>
              </div>
              <div className="text-center">
                <div className="mb-4 flex justify-center">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary text-white text-lg font-bold">
                    3
                  </div>
                </div>
                <h3 className="mb-2 text-lg font-semibold">Try & Save</h3>
                <p className="text-sm text-gray-600">
                  See yourself in different frames and save your favorites
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="rounded-2xl bg-gradient-to-r from-blue-600 to-purple-600 p-12 text-center text-white">
            <h2 className="mb-4 text-3xl font-bold sm:text-4xl">
              Ready to Find Your Style?
            </h2>
            <p className="mb-8 text-lg opacity-90">
              Join thousands who've found their perfect frames with GlassVTO
            </p>
            <Button size="lg" variant="secondary" asChild>
              <Link href="/catalog">
                Start Shopping
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
