'use client'

import React, { useRef, useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

export function MediaPipeTest() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [status, setStatus] = useState<string>('Not started')
  const [faceDetector, setFaceDetector] = useState<any>(null)
  const [isRunning, setIsRunning] = useState(false)

  // Initialize MediaPipe
  const initializeMediaPipe = async () => {
    try {
      setStatus('Loading MediaPipe...')
      
      // Import MediaPipe
      const { FaceLandmarker, FilesetResolver } = await import('@mediapipe/tasks-vision')
      
      const vision = await FilesetResolver.forVisionTasks(
        'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@latest/wasm'
      )

      const faceLandmarker = await FaceLandmarker.createFromOptions(vision, {
        baseOptions: {
          modelAssetPath: 'https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task',
          delegate: 'CPU'
        },
        outputFaceBlendshapes: false,
        runningMode: 'VIDEO',
        numFaces: 1
      })

      setFaceDetector(faceLandmarker)
      setStatus('MediaPipe loaded successfully!')
      
    } catch (error) {
      console.error('MediaPipe initialization error:', error)
      setStatus(`MediaPipe error: ${error.message}`)
    }
  }

  // Start camera
  const startCamera = async () => {
    try {
      setStatus('Starting camera...')
      
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: 'user'
        }
      })

      if (videoRef.current) {
        videoRef.current.srcObject = stream
        await videoRef.current.play()
        
        // Wait for video metadata
        await new Promise((resolve) => {
          if (videoRef.current!.readyState >= 1) {
            resolve(true)
          } else {
            videoRef.current!.addEventListener('loadedmetadata', () => resolve(true), { once: true })
          }
        })
        
        // Wait additional time for video to stabilize
        await new Promise(resolve => setTimeout(resolve, 1000))

        setStatus(`Camera started: ${videoRef.current.videoWidth}x${videoRef.current.videoHeight}`)
      }
      
    } catch (error) {
      console.error('Camera error:', error)
      setStatus(`Camera error: ${error.message}`)
    }
  }

  // Test face detection with comprehensive validation
  const testDetection = () => {
    if (!faceDetector || !videoRef.current) {
      setStatus('MediaPipe or video not ready')
      return
    }

    // Validate video before starting detection
    const video = videoRef.current
    if (video.readyState < video.HAVE_ENOUGH_DATA) {
      setStatus(`Video not ready: readyState=${video.readyState}`)
      return
    }

    if (video.videoWidth <= 0 || video.videoHeight <= 0) {
      setStatus(`Invalid video dimensions: ${video.videoWidth}x${video.videoHeight}`)
      return
    }

    setStatus(`Starting detection with video: ${video.videoWidth}x${video.videoHeight}`)
    setIsRunning(true)
    let frameCount = 0
    let lastTime = performance.now()

    const detect = () => {
      if (!videoRef.current || !faceDetector || !isRunning) return

      const video = videoRef.current

      // Validate video state before each detection
      if (video.readyState < video.HAVE_ENOUGH_DATA ||
          video.videoWidth <= 0 ||
          video.videoHeight <= 0 ||
          video.paused ||
          video.ended) {
        setStatus(`Video state invalid: ${video.videoWidth}x${video.videoHeight}, readyState=${video.readyState}`)
        setTimeout(() => requestAnimationFrame(detect), 100)
        return
      }

      try {
        const timestamp = performance.now()
        const results = faceDetector.detectForVideo(video, timestamp)
        
        frameCount++
        
        if (results && results.faceLandmarks && results.faceLandmarks.length > 0) {
          const landmarks = results.faceLandmarks[0]
          
          // Draw landmarks on canvas
          if (canvasRef.current) {
            const canvas = canvasRef.current
            const ctx = canvas.getContext('2d')
            if (ctx) {
              // Set canvas size to match video
              canvas.width = videoRef.current.videoWidth
              canvas.height = videoRef.current.videoHeight
              
              // Clear canvas
              ctx.clearRect(0, 0, canvas.width, canvas.height)
              
              // Draw landmarks
              ctx.fillStyle = 'red'
              landmarks.forEach((landmark: any, index: number) => {
                const x = landmark.x * canvas.width
                const y = landmark.y * canvas.height
                
                // Draw key points larger
                if ([168, 143, 372, 2].includes(index)) {
                  ctx.fillStyle = 'blue'
                  ctx.fillRect(x - 3, y - 3, 6, 6)
                  ctx.fillStyle = 'white'
                  ctx.font = '12px Arial'
                  ctx.fillText(index.toString(), x + 5, y - 5)
                } else {
                  ctx.fillStyle = 'red'
                  ctx.fillRect(x - 1, y - 1, 2, 2)
                }
              })
            }
          }
          
          // Update status every second
          if (timestamp - lastTime > 1000) {
            setStatus(`Detecting faces: ${frameCount} fps, ${results.faceLandmarks.length} face(s)`)
            frameCount = 0
            lastTime = timestamp
          }
        } else {
          setStatus('No face detected')
        }
        
        if (isRunning) {
          requestAnimationFrame(detect)
        }
        
      } catch (error) {
        console.error('Detection error:', error)
        setStatus(`Detection error: ${error.message}`)
      }
    }

    detect()
  }

  const stopDetection = () => {
    setIsRunning(false)
    setStatus('Detection stopped')
  }

  useEffect(() => {
    initializeMediaPipe()
  }, [])

  return (
    <Card className="p-6">
      <h2 className="text-xl font-bold mb-4">MediaPipe Face Detection Test</h2>
      
      <div className="space-y-4">
        <div className="text-sm font-mono bg-gray-100 p-2 rounded">
          Status: {status}
        </div>
        
        <div className="flex gap-2">
          <Button onClick={startCamera} disabled={!faceDetector}>
            Start Camera
          </Button>
          <Button onClick={testDetection} disabled={!faceDetector || !videoRef.current}>
            Test Detection
          </Button>
          <Button onClick={stopDetection} variant="outline">
            Stop Detection
          </Button>
        </div>
        
        <div className="relative">
          <video
            ref={videoRef}
            className="w-full max-w-md border rounded"
            style={{ transform: 'scaleX(-1)' }}
            playsInline
            muted
          />
          <canvas
            ref={canvasRef}
            className="absolute inset-0 w-full max-w-md border rounded pointer-events-none"
            style={{ transform: 'scaleX(-1)' }}
          />
        </div>
        
        <div className="text-xs text-gray-600">
          <p>This test component helps debug MediaPipe face detection issues.</p>
          <p>Blue dots show key landmarks: 168 (midEye), 143 (leftEye), 372 (rightEye), 2 (noseBottom)</p>
          <p>Red dots show all other face landmarks.</p>
        </div>
      </div>
    </Card>
  )
}
