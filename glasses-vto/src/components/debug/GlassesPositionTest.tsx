'use client'

import React, { useRef, useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Slider } from '@/components/ui/slider'
import { GlassesRenderer } from '@/lib/three/glasses-renderer'

export function GlassesPositionTest() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const rendererRef = useRef<GlassesRenderer | null>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const [position, setPosition] = useState({ x: 320, y: 0, z: -100 })
  const [scale, setScale] = useState(15)

  useEffect(() => {
    if (canvasRef.current && !rendererRef.current) {
      // Initialize renderer with standard video dimensions
      rendererRef.current = new GlassesRenderer(canvasRef.current, 640, 480)
      
      // Load simple glasses model for testing
      rendererRef.current.loadGlassesModel('/models/simple-glasses.gltf')
        .then(() => {
          setIsLoaded(true)
          console.log('✅ Glasses model loaded for position test')
          
          // Initial test position
          if (rendererRef.current) {
            rendererRef.current.manualTestPosition(position.x, position.y, position.z, scale)
          }
        })
        .catch((error) => {
          console.error('❌ Failed to load glasses model:', error)
        })
    }

    return () => {
      if (rendererRef.current) {
        rendererRef.current.dispose()
      }
    }
  }, [])

  const updatePosition = (newPosition: Partial<typeof position>) => {
    const updatedPosition = { ...position, ...newPosition }
    setPosition(updatedPosition)
    
    if (rendererRef.current && isLoaded) {
      rendererRef.current.manualTestPosition(
        updatedPosition.x, 
        updatedPosition.y, 
        updatedPosition.z, 
        scale
      )
    }
  }

  const updateScale = (newScale: number) => {
    setScale(newScale)
    
    if (rendererRef.current && isLoaded) {
      rendererRef.current.manualTestPosition(position.x, position.y, position.z, newScale)
    }
  }

  const testCenterPosition = () => {
    if (rendererRef.current && isLoaded) {
      rendererRef.current.testPositionGlasses()
    }
  }

  const testFacePosition = () => {
    // Simulate face at center of screen using FIXED coordinates
    const faceX = 320 // Center X of 640px width
    const faceY = 0   // Center Y using fixed calculation: (480/2) - (480/2) = 0
    const faceZ = -100 // In front of camera

    updatePosition({ x: faceX, y: faceY, z: faceZ })
  }

  return (
    <Card className="p-6">
      <h2 className="text-xl font-bold mb-4">3D Glasses Position Test</h2>
      
      <div className="space-y-4">
        <div className="text-sm font-mono bg-gray-100 p-2 rounded">
          Status: {isLoaded ? 'Glasses loaded ✅' : 'Loading glasses...'}
        </div>
        
        <div className="flex gap-2 mb-4">
          <Button onClick={testCenterPosition} disabled={!isLoaded}>
            Test Center Position
          </Button>
          <Button onClick={testFacePosition} disabled={!isLoaded}>
            Test Face Position
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <canvas
              ref={canvasRef}
              width={640}
              height={480}
              className="border rounded bg-black"
              style={{ maxWidth: '100%', height: 'auto' }}
            />
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">X Position: {position.x}</label>
              <Slider
                value={[position.x]}
                onValueChange={([value]) => updatePosition({ x: value })}
                min={0}
                max={640}
                step={1}
                className="mt-2"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">Y Position: {position.y}</label>
              <Slider
                value={[position.y]}
                onValueChange={([value]) => updatePosition({ y: value })}
                min={-240}
                max={240}
                step={1}
                className="mt-2"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">Z Position: {position.z}</label>
              <Slider
                value={[position.z]}
                onValueChange={([value]) => updatePosition({ z: value })}
                min={-500}
                max={100}
                step={1}
                className="mt-2"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium">Scale: {scale}</label>
              <Slider
                value={[scale]}
                onValueChange={([value]) => updateScale(value)}
                min={1}
                max={50}
                step={1}
                className="mt-2"
              />
            </div>
            
            <div className="text-xs text-gray-600 space-y-1">
              <p><strong>Expected ranges (FIXED coordinates):</strong></p>
              <p>X: 0-640 (video width)</p>
              <p>Y: -240 to +240 (center=0, positive=up)</p>
              <p>Z: -500-100 (depth, negative = away from camera)</p>
              <p>Scale: 1-50 (size multiplier)</p>
              <p><strong>Face center should be around:</strong> X=320, Y=0</p>
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}
