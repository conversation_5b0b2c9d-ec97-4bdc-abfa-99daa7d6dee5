import Link from 'next/link'
import { Glasses, Github, Twitter, Instagram } from 'lucide-react'

export function Footer() {
  return (
    <footer className="border-t bg-gray-50">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Glasses className="h-6 w-6" />
              <span className="text-xl font-bold">GlassVTO</span>
            </div>
            <p className="text-sm text-gray-600">
              Experience glasses virtually with our advanced try-on technology.
            </p>
          </div>

          {/* Shop */}
          <div className="space-y-4">
            <h3 className="font-semibold">Shop</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/catalog" className="text-gray-600 hover:text-primary">
                  All Products
                </Link>
              </li>
              <li>
                <Link href="/catalog?gender=men" className="text-gray-600 hover:text-primary">
                  Men's Glasses
                </Link>
              </li>
              <li>
                <Link href="/catalog?gender=women" className="text-gray-600 hover:text-primary">
                  Women's Glasses
                </Link>
              </li>
              <li>
                <Link href="/catalog?featured=true" className="text-gray-600 hover:text-primary">
                  Featured
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div className="space-y-4">
            <h3 className="font-semibold">Support</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/help/try-on" className="text-gray-600 hover:text-primary">
                  How Try-On Works
                </Link>
              </li>
              <li>
                <Link href="/help/faq" className="text-gray-600 hover:text-primary">
                  FAQ
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-600 hover:text-primary">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-gray-600 hover:text-primary">
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>

          {/* Connect */}
          <div className="space-y-4">
            <h3 className="font-semibold">Connect</h3>
            <div className="flex space-x-4">
              <a
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-600 hover:text-primary"
              >
                <Github className="h-5 w-5" />
              </a>
              <a
                href="https://twitter.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-600 hover:text-primary"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a
                href="https://instagram.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-600 hover:text-primary"
              >
                <Instagram className="h-5 w-5" />
              </a>
            </div>
            <p className="text-sm text-gray-600">
              Stay updated with our latest collections and features.
            </p>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t text-center text-sm text-gray-600">
          <p>&copy; 2024 GlassVTO. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}