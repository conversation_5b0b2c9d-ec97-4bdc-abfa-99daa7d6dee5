'use client'

import { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import { 
  Upload, 
  File, 
  X, 
  Check, 
  AlertCircle,
  Loader2
} from 'lucide-react'

interface FileUploadProps {
  bucket: 'models' | 'products'
  path?: string
  accept?: string
  maxSize?: number // in MB
  onUploadComplete?: (result: UploadResult) => void
  onUploadError?: (error: string) => void
  className?: string
  disabled?: boolean
}

interface UploadResult {
  path: string
  publicUrl: string
  size: number
  type: string
  name: string
}

const defaultAccept = {
  models: '.glb,.gltf',
  products: '.jpg,.jpeg,.png,.webp'
}

const defaultMaxSize = {
  models: 10, // 10MB
  products: 5  // 5MB
}

export default function FileUpload({
  bucket,
  path = '',
  accept,
  maxSize,
  onUploadComplete,
  onUploadError,
  className = '',
  disabled = false
}: FileUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [progress, setProgress] = useState(0)
  const [uploadedFile, setUploadedFile] = useState<UploadResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [dragOver, setDragOver] = useState(false)
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const acceptTypes = accept || defaultAccept[bucket]
  const maxSizeLimit = maxSize || defaultMaxSize[bucket]

  const handleFileSelect = (file: File) => {
    if (disabled) return
    
    setError(null)
    
    // Validate file size
    if (file.size > maxSizeLimit * 1024 * 1024) {
      const error = `File too large. Maximum size is ${maxSizeLimit}MB.`
      setError(error)
      onUploadError?.(error)
      return
    }
    
    // Validate file type
    const fileName = file.name.toLowerCase()
    const allowedExtensions = acceptTypes.split(',').map(ext => ext.trim())
    const hasValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext))
    
    if (!hasValidExtension) {
      const error = `Invalid file type. Allowed: ${acceptTypes}`
      setError(error)
      onUploadError?.(error)
      return
    }
    
    uploadFile(file)
  }

  const uploadFile = async (file: File) => {
    setUploading(true)
    setProgress(0)
    setError(null)
    
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('bucket', bucket)
      
      // Generate path if not provided
      const uploadPath = path || `${Date.now()}-${file.name}`
      formData.append('path', uploadPath)
      
      const response = await fetch('/api/admin/upload', {
        method: 'POST',
        body: formData
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Upload failed')
      }
      
      const result: UploadResult = await response.json()
      setUploadedFile(result)
      setProgress(100)
      onUploadComplete?.(result)
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'
      setError(errorMessage)
      onUploadError?.(errorMessage)
    } finally {
      setUploading(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const clearUpload = () => {
    setUploadedFile(null)
    setError(null)
    setProgress(0)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        className={`
          border-2 border-dashed rounded-lg p-6 text-center transition-colors
          ${dragOver ? 'border-primary bg-primary/5' : 'border-gray-300'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:border-primary hover:bg-primary/5'}
          ${error ? 'border-red-300 bg-red-50' : ''}
          ${uploadedFile ? 'border-green-300 bg-green-50' : ''}
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={acceptTypes}
          onChange={handleInputChange}
          className="hidden"
          disabled={disabled}
        />
        
        {uploading ? (
          <div className="space-y-2">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
            <p className="text-sm text-gray-600">Uploading...</p>
            <Progress value={progress} className="w-full max-w-xs mx-auto" />
          </div>
        ) : uploadedFile ? (
          <div className="space-y-2">
            <Check className="h-8 w-8 mx-auto text-green-600" />
            <p className="text-sm font-medium text-green-700">Upload successful!</p>
            <p className="text-xs text-gray-500">{uploadedFile.name}</p>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                clearUpload()
              }}
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          </div>
        ) : error ? (
          <div className="space-y-2">
            <AlertCircle className="h-8 w-8 mx-auto text-red-600" />
            <p className="text-sm font-medium text-red-700">Upload failed</p>
            <p className="text-xs text-red-600">{error}</p>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                setError(null)
              }}
            >
              Try again
            </Button>
          </div>
        ) : (
          <div className="space-y-2">
            <Upload className="h-8 w-8 mx-auto text-gray-400" />
            <p className="text-sm font-medium text-gray-700">
              Drop your {bucket === 'models' ? '3D model' : 'image'} here or click to browse
            </p>
            <p className="text-xs text-gray-500">
              Supported: {acceptTypes} • Max size: {maxSizeLimit}MB
            </p>
          </div>
        )}
      </div>
      
      {/* File Info */}
      {uploadedFile && (
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center space-x-3">
            <File className="h-5 w-5 text-gray-400" />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {uploadedFile.name}
              </p>
              <p className="text-xs text-gray-500">
                {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB • {uploadedFile.type}
              </p>
            </div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={clearUpload}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Path Display */}
          <div className="mt-2 pt-2 border-t border-gray-200">
            <Label className="text-xs text-gray-500">Storage Path:</Label>
            <p className="text-xs font-mono text-gray-700 bg-white px-2 py-1 rounded mt-1">
              {bucket}/{uploadedFile.path}
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
