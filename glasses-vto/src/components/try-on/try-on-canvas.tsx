'use client'

import React, { useEffect, useRef, useState, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Slider } from '@/components/ui/slider'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Camera, CameraOff, Download, Loader2, RotateCcw, Sparkles } from 'lucide-react'
import { FaceDetection } from '@/lib/mediapipe/face-detection'
import { GlassesRenderer } from '@/lib/three/glasses-renderer'
import { useTryOnStore } from '@/lib/store'
import { toast } from 'sonner'

interface TryOnCanvasProps {
  modelUrl?: string
  productId: string
  variantId: string
  onSnapshot?: (imageData: string) => void
}

export function TryOnCanvas({
  modelUrl = '/models/default-glasses.gltf',
  productId,
  variantId,
  onSnapshot
}: TryOnCanvasProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const overlayCanvasRef = useRef<HTMLCanvasElement>(null)
  const faceDetectorRef = useRef<FaceDetection | null>(null)
  const glassesRendererRef = useRef<GlassesRenderer | null>(null)
  
  const [isLoading, setIsLoading] = useState(true)
  const [isCameraOn, setIsCameraOn] = useState(false)
  const [showPermissionDialog, setShowPermissionDialog] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isDetecting, setIsDetecting] = useState(false)

  // Debug states
  const [debugMode, setDebugMode] = useState(false)
  const [debugStats, setDebugStats] = useState({
    fps: 0,
    faceCount: 0,
    lastDetection: 0,
    renderCount: 0,
    errors: 0
  })
  const [showDebugOverlay, setShowDebugOverlay] = useState(false)
  
  const { tryOnSettings, setTryOnSettings } = useTryOnStore()
  const animationFrameRef = useRef<number | null>(null)

  // Debug refs
  const fpsCounterRef = useRef({ frames: 0, lastTime: Date.now() })
  const debugIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Initialize camera
  const startCamera = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'user'
        }
      })

      if (videoRef.current) {
        videoRef.current.srcObject = stream
        await videoRef.current.play()

        // Wait for video metadata to load completely
        await new Promise((resolve) => {
          const video = videoRef.current!

          const checkVideoReady = () => {
            if (video.readyState >= video.HAVE_ENOUGH_DATA &&
                video.videoWidth > 0 &&
                video.videoHeight > 0) {
              console.log('✅ Video fully ready:', {
                readyState: video.readyState,
                dimensions: `${video.videoWidth}x${video.videoHeight}`,
                currentTime: video.currentTime
              })
              resolve(true)
            } else {
              console.log('⏳ Waiting for video...', {
                readyState: video.readyState,
                dimensions: `${video.videoWidth}x${video.videoHeight}`
              })
              setTimeout(checkVideoReady, 100) // Check every 100ms
            }
          }

          if (video.readyState >= video.HAVE_ENOUGH_DATA && video.videoWidth > 0) {
            checkVideoReady()
          } else {
            video.addEventListener('loadedmetadata', checkVideoReady, { once: true })
            video.addEventListener('canplay', checkVideoReady, { once: true })
            video.addEventListener('canplaythrough', checkVideoReady, { once: true })

            // Fallback timeout
            setTimeout(() => {
              console.warn('⚠️ Video ready timeout, proceeding anyway')
              resolve(true)
            }, 5000)
          }
        })

        // Additional wait to ensure video is stable
        await new Promise(resolve => setTimeout(resolve, 500))

        // Resize renderer to match video dimensions
        if (glassesRendererRef.current && videoRef.current.videoWidth > 0) {
          const videoWidth = videoRef.current.videoWidth
          const videoHeight = videoRef.current.videoHeight

          // Update canvas size to match video
          if (overlayCanvasRef.current) {
            overlayCanvasRef.current.width = videoWidth
            overlayCanvasRef.current.height = videoHeight
          }

          glassesRendererRef.current.resize(videoWidth, videoHeight)
          console.log('🎥 Resized renderer to video dimensions:', {
            width: videoWidth,
            height: videoHeight,
            rendererExists: !!glassesRendererRef.current
          })
        } else {
          console.warn('⚠️ Cannot resize renderer:', {
            hasRenderer: !!glassesRendererRef.current,
            videoWidth: videoRef.current?.videoWidth,
            videoHeight: videoRef.current?.videoHeight
          })
        }

        setIsCameraOn(true)
        console.log('🎬 Camera started successfully')

              // Start debug monitoring if enabled
        if (debugMode) {
          startDebugMonitoring()
        }

        // Start detection loop after camera is ready
        if (faceDetectorRef.current && glassesRendererRef.current) {
          console.log('🚀 Starting detection loop after camera ready...')
          if (animationFrameRef.current) {
            cancelAnimationFrame(animationFrameRef.current)
          }
          animationFrameRef.current = requestAnimationFrame(detectAndRender)
        } else {
          console.warn('⚠️ Cannot start detection - missing components:', {
            hasFaceDetector: !!faceDetectorRef.current,
            hasGlassesRenderer: !!glassesRendererRef.current
          })
        }
      }
    } catch (err: any) {
      console.error('Camera error:', err)
      if (err.name === 'NotAllowedError') {
        setShowPermissionDialog(true)
      } else {
        setError('Failed to access camera. Please check your device settings.')
      }
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Stop camera
  const stopCamera = useCallback(() => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream
      stream.getTracks().forEach(track => track.stop())
      videoRef.current.srcObject = null
      setIsCameraOn(false)
    }
  }, [])

  // Debug monitoring functions
  const startDebugMonitoring = useCallback(() => {
    if (debugIntervalRef.current) {
      clearInterval(debugIntervalRef.current)
    }

    debugIntervalRef.current = setInterval(() => {
      const now = Date.now()
      const timeDiff = now - fpsCounterRef.current.lastTime
      const fps = Math.round((fpsCounterRef.current.frames * 1000) / timeDiff)

      setDebugStats(prev => ({
        ...prev,
        fps
      }))

      // Reset counters
      fpsCounterRef.current.frames = 0
      fpsCounterRef.current.lastTime = now

      if (debugMode) {
        console.log('📊 Debug Stats:', {
          fps,
          faceCount: debugStats.faceCount,
          renderCount: debugStats.renderCount,
          errors: debugStats.errors,
          cameraOn: isCameraOn,
          detecting: isDetecting
        })
      }
    }, 1000)
  }, [debugMode, debugStats, isCameraOn, isDetecting])

  const updateDebugStats = useCallback((update: Partial<typeof debugStats>) => {
    setDebugStats(prev => ({ ...prev, ...update }))
  }, [])

  // Initialize Face Detection and Three.js
  useEffect(() => {
    const initializeComponents = async () => {
      try {
        setIsLoading(true)

        console.log('🚀 Initializing components with:', {
          productId,
          variantId,
          modelUrl,
          hasModelUrl: !!modelUrl
        })

        // Initialize face detector
        if (!faceDetectorRef.current) {
          console.log('Initializing face detector...')
          faceDetectorRef.current = new FaceDetection()
          await faceDetectorRef.current.initialize()
          console.log('Face detector initialized successfully')
        }

        // Initialize Three.js renderer with ACTUAL video dimensions
        if (overlayCanvasRef.current && !glassesRendererRef.current) {
          // Use default dimensions first, will resize later when video is ready
          const defaultWidth = 640
          const defaultHeight = 480

          console.log('🎨 Setting up Three.js canvas with default dimensions (will resize later):', {
            canvas: overlayCanvasRef.current,
            defaultDimensions: `${defaultWidth}x${defaultHeight}`
          })

          // Set initial canvas size
          overlayCanvasRef.current.width = defaultWidth
          overlayCanvasRef.current.height = defaultHeight
          overlayCanvasRef.current.style.width = '100%'
          overlayCanvasRef.current.style.height = '100%'

          try {
            glassesRendererRef.current = new GlassesRenderer(
              overlayCanvasRef.current,
              defaultWidth,
              defaultHeight
            )

            console.log('✅ GlassesRenderer created successfully')

          } catch (rendererError) {
            console.error('❌ Failed to create GlassesRenderer:', rendererError)
            throw rendererError
          }

          // Load glasses model asynchronously (don't block initialization)
          const loadModel = async () => {
            if (!glassesRendererRef.current) return

            if (modelUrl) {
              console.log('🔄 Loading glasses model:', modelUrl)
              try {
                await glassesRendererRef.current.loadGlassesModel(modelUrl, productId)
                console.log('✅ Glasses model loaded successfully')
              } catch (modelError) {
                console.error('❌ Model loading failed:', modelError)
                // Try simple model for testing
                console.log('🔄 Trying simple test model...')
                try {
                  await glassesRendererRef.current.loadGlassesModel('/models/simple-glasses.gltf', productId)
                  console.log('✅ Simple test model loaded')
                } catch (fallbackError) {
                  console.error('❌ Simple model also failed:', fallbackError)
                }
              }
            } else {
              console.warn('⚠️ No model URL provided, trying simple test model')
              try {
                await glassesRendererRef.current.loadGlassesModel('/models/simple-glasses.gltf', productId)
                console.log('✅ Simple test model loaded')
              } catch (fallbackError) {
                console.error('❌ Simple model failed:', fallbackError)
              }
            }
          }

          // Start loading model in background
          loadModel().catch(console.error)

          console.log('Three.js renderer initialized with standard dimensions: 640x480')

          // Expose debugging methods to window for console testing
          if (typeof window !== 'undefined') {
            (window as any).debugGlasses = {
              testCenter: () => glassesRendererRef.current?.testPositionGlasses(),
              testFace: (x = 0.5, y = 0.4) => glassesRendererRef.current?.testFacePosition(x, y, 640, 480),
              testManual: (x: number, y: number, z: number, scale = 30) => glassesRendererRef.current?.manualTestPosition(x, y, z, scale),
              getCamera: () => glassesRendererRef.current?.camera?.position,
              getGlasses: () => glassesRendererRef.current?.glassesModel?.position,
              toggleDebug: () => setDebugMode(!debugMode),
              toggleOverlay: () => setShowDebugOverlay(!showDebugOverlay),
              getStats: () => debugStats,
              resetStats: () => setDebugStats({ fps: 0, faceCount: 0, lastDetection: 0, renderCount: 0, errors: 0 })
            }
            console.log('🔧 Debug methods exposed to window.debugGlasses:', {
              testCenter: 'Test center position',
              testFace: 'Test face position (x, y normalized 0-1)',
              testManual: 'Test manual position (x, y, z, scale)',
              getCamera: 'Get camera position',
              getGlasses: 'Get glasses position',
              toggleDebug: 'Toggle debug mode',
              toggleOverlay: 'Toggle debug overlay',
              getStats: 'Get current debug stats',
              resetStats: 'Reset debug statistics'
            })
          }
        }

        setIsLoading(false)

        // Start detection loop after everything is initialized
        console.log('🚀 Starting detection loop...')
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current)
        }
        animationFrameRef.current = requestAnimationFrame(detectAndRender)

      } catch (err) {
        console.error('Initialization error:', err)
        setError('Failed to initialize AR components')
        setIsLoading(false)
      }
    }

    initializeComponents()

    return () => {
      // Cleanup animation frame
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
        animationFrameRef.current = null
      }

      // Cleanup components
      if (faceDetectorRef.current) {
        faceDetectorRef.current.dispose()
        faceDetectorRef.current = null
      }
      if (glassesRendererRef.current) {
        glassesRendererRef.current.dispose()
        glassesRendererRef.current = null
      }
    }
  }, [modelUrl])

  // Main detection and rendering loop with enhanced error handling
  const detectAndRender = useCallback(() => {
    if (!videoRef.current || !faceDetectorRef.current || !glassesRendererRef.current) {
      if (debugMode) {
        console.warn('Missing required refs for detection:', {
          hasVideo: !!videoRef.current,
          hasFaceDetector: !!faceDetectorRef.current,
          hasGlassesRenderer: !!glassesRendererRef.current,
          videoReady: videoRef.current?.readyState,
          videoDimensions: videoRef.current ? `${videoRef.current.videoWidth}x${videoRef.current.videoHeight}` : 'N/A'
        })
      }

      // Try to continue the loop in case refs become available
      if (videoRef.current) {
        animationFrameRef.current = requestAnimationFrame(detectAndRender)
      }
      return
    }

    // Comprehensive video readiness check
    const video = videoRef.current
    if (video.readyState < video.HAVE_ENOUGH_DATA ||
        video.videoWidth <= 0 ||
        video.videoHeight <= 0 ||
        video.paused ||
        video.ended) {

      // Log detailed video state for debugging
      if (video.videoWidth <= 0 || video.videoHeight <= 0) {
        console.warn('⚠️ Video dimensions invalid:', {
          width: video.videoWidth,
          height: video.videoHeight,
          readyState: video.readyState
        })
      }

      animationFrameRef.current = requestAnimationFrame(detectAndRender)
      return
    }

    try {
      // Update FPS counter
      fpsCounterRef.current.frames++

      setIsDetecting(true)

      // Detect faces with error handling
      const results = faceDetectorRef.current.detectFaces(video)

      if (results && results.faceLandmarks && results.faceLandmarks.length > 0) {
        const landmarks = results.faceLandmarks
        const keyPoints = faceDetectorRef.current.getFaceKeyPoints(landmarks)

        // Update debug stats
        updateDebugStats({
          faceCount: results.faceLandmarks.length,
          lastDetection: Date.now()
        })

        if (keyPoints) {
          const rotation = faceDetectorRef.current.calculateFaceRotation(keyPoints)
          const baseScale = faceDetectorRef.current.calculateGlassesScale(keyPoints)
          const finalScale = baseScale * (tryOnSettings.zoom || 1)

          // Enhanced debug logging for positioning issues
          if (Math.abs(keyPoints.midEye.x - 0.5) > 0.4 || Math.abs(keyPoints.midEye.y - 0.5) > 0.4 || Math.random() < 0.02) {
            console.log('🎯 Face detection data for positioning fix:', {
              midEye: keyPoints.midEye,
              leftEye: keyPoints.leftEye,
              rightEye: keyPoints.rightEye,
              noseBottom: keyPoints.noseBottom,
              scale: finalScale,
              rotation: rotation,
              videoSize: { width: video.videoWidth, height: video.videoHeight },
              // Calculate expected screen coordinates
              expectedScreenX: keyPoints.midEye.x * video.videoWidth,
              expectedScreenY: keyPoints.midEye.y * video.videoHeight,
              // Show where glasses should be positioned with FIXED Y calculation
              expectedGlassesX: keyPoints.midEye.x * video.videoWidth,
              expectedGlassesY: (video.videoHeight / 2) - (keyPoints.midEye.y * video.videoHeight)
            })
          }

          // Use Benson Ruan's proven accurate method
          glassesRendererRef.current.updateFromFaceLandmarksHybrid(
            keyPoints,
            rotation,
            finalScale,
            video,
            {
              x: tryOnSettings.offsetX || 0,
              y: tryOnSettings.offsetY || 0,
              z: tryOnSettings.offsetZ || 0
            }
          )

          // Apply brightness setting with better filtering
          if (overlayCanvasRef.current) {
            const brightness = tryOnSettings.brightness / 100
            overlayCanvasRef.current.style.filter = `brightness(${brightness}) contrast(1.1) saturate(1.05)`
          }

          setIsDetecting(true) // Face detected successfully
        }
      } else {
        // No face detected
        setIsDetecting(false)
        updateDebugStats({ faceCount: 0 })
        if (debugMode) {
          console.log('No face detected in current frame')
        }
      }

      // Always render, even without face detection
      glassesRendererRef.current.render()
      updateDebugStats(prev => ({ ...prev, renderCount: prev.renderCount + 1 }))

    } catch (error) {
      console.error('Error in detection loop:', error)
      setIsDetecting(false)
      updateDebugStats(prev => ({ ...prev, errors: prev.errors + 1 }))

      // Still try to render
      try {
        glassesRendererRef.current.render()
      } catch (renderError) {
        console.error('Render error:', renderError)
      }
    }

    // Continue loop
    animationFrameRef.current = requestAnimationFrame(detectAndRender)
  }, [tryOnSettings, debugMode, updateDebugStats])

  // Start/stop detection loop when camera state changes
  useEffect(() => {
    if (isCameraOn && !isLoading) {
      detectAndRender()
    } else {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
        animationFrameRef.current = null
      }
      setIsDetecting(false)
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
        animationFrameRef.current = null
      }
      if (debugIntervalRef.current) {
        clearInterval(debugIntervalRef.current)
        debugIntervalRef.current = null
      }
    }
  }, [isCameraOn, isLoading, detectAndRender])

  // Handle debug mode changes
  useEffect(() => {
    if (debugMode && isCameraOn) {
      startDebugMonitoring()
    } else if (debugIntervalRef.current) {
      clearInterval(debugIntervalRef.current)
      debugIntervalRef.current = null
    }

    return () => {
      if (debugIntervalRef.current) {
        clearInterval(debugIntervalRef.current)
        debugIntervalRef.current = null
      }
    }
  }, [debugMode, isCameraOn, startDebugMonitoring])

  // Handle snapshot with proper mirroring and scaling
  const takeSnapshot = useCallback(() => {
    if (!canvasRef.current || !videoRef.current || !overlayCanvasRef.current || !glassesRendererRef.current) return

    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size to match video dimensions
    const videoWidth = videoRef.current.videoWidth
    const videoHeight = videoRef.current.videoHeight
    canvas.width = videoWidth
    canvas.height = videoHeight

    // Save context state
    ctx.save()

    // Apply mirroring to match the display
    ctx.scale(-1, 1)
    ctx.translate(-videoWidth, 0)

    // Draw video frame (mirrored)
    ctx.drawImage(videoRef.current, 0, 0, videoWidth, videoHeight)

    // Restore context for overlay
    ctx.restore()
    ctx.save()

    // Apply same mirroring for glasses overlay
    ctx.scale(-1, 1)
    ctx.translate(-videoWidth, 0)

    // Draw glasses overlay with proper scaling
    const overlayCanvas = overlayCanvasRef.current
    ctx.drawImage(overlayCanvas, 0, 0, videoWidth, videoHeight)

    // Restore context
    ctx.restore()

    // Get image data
    const imageData = canvas.toDataURL('image/png', 0.95) // High quality

    // Callback or download
    if (onSnapshot) {
      onSnapshot(imageData)
    } else {
      // Download image
      const link = document.createElement('a')
      link.href = imageData
      link.download = `glasses-tryon-${Date.now()}.png`
      link.click()
    }

    toast.success('Snapshot saved!')
  }, [onSnapshot])

  return (
    <Card className="relative overflow-hidden">
      <div className="relative aspect-video bg-black">
        {/* Video Feed */}
        <video
          ref={videoRef}
          className="absolute inset-0 w-full h-full object-cover mirror"
          style={{ transform: 'scaleX(-1)' }} // Mirror for selfie view
          playsInline
          muted
        />
        
        {/* Three.js Overlay Canvas */}
        <canvas
          ref={overlayCanvasRef}
          className="absolute inset-0 w-full h-full pointer-events-none"
          style={{
            transform: 'scaleX(-1)' // Mirror overlay too
          }}
        />
        
        {/* Hidden canvas for snapshots */}
        <canvas ref={canvasRef} className="hidden" />
        
        {/* Loading Overlay */}
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50">
            <Loader2 className="h-8 w-8 animate-spin text-white" />
          </div>
        )}

        {/* Debug Overlay */}
        {showDebugOverlay && (
          <div className="absolute top-2 left-2 bg-black/80 text-white text-xs p-2 rounded font-mono">
            <div>FPS: {debugStats.fps}</div>
            <div>Faces: {debugStats.faceCount}</div>
            <div>Renders: {debugStats.renderCount}</div>
            <div>Errors: {debugStats.errors}</div>
            <div>Camera: {isCameraOn ? 'ON' : 'OFF'}</div>
            <div>Detecting: {isDetecting ? 'YES' : 'NO'}</div>
            <div>Last Detection: {debugStats.lastDetection > 0 ? `${Date.now() - debugStats.lastDetection}ms ago` : 'Never'}</div>
          </div>
        )}
        
        {/* Error Message */}
        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/80">
            <div className="text-center text-white p-4">
              <p className="mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>
                <RotateCcw className="mr-2 h-4 w-4" />
                Retry
              </Button>
            </div>
          </div>
        )}
        
        {/* Camera Off State */}
        {!isCameraOn && !isLoading && !error && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
            <div className="text-center">
              <CameraOff className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-400 mb-4">Camera is off</p>
              <Button onClick={startCamera}>
                <Camera className="mr-2 h-4 w-4" />
                Turn On Camera
              </Button>
            </div>
          </div>
        )}
        
        {/* Detection Indicator */}
        {isDetecting && (
          <div className="absolute top-4 left-4 flex items-center gap-2 bg-green-500/20 backdrop-blur px-3 py-1 rounded-full">
            <Sparkles className="h-4 w-4 text-green-400" />
            <span className="text-sm text-green-400">Face Detected</span>
          </div>
        )}
      </div>
      
      {/* Controls */}
      <div className="p-4 space-y-4 bg-white">
        <div className="flex gap-2">
          {!isCameraOn ? (
            <Button onClick={startCamera} className="flex-1">
              <Camera className="mr-2 h-4 w-4" />
              Start Camera
            </Button>
          ) : (
            <Button onClick={stopCamera} variant="outline" className="flex-1">
              <CameraOff className="mr-2 h-4 w-4" />
              Stop Camera
            </Button>
          )}

          {/* Debug Controls */}
          <Button
            onClick={() => setDebugMode(!debugMode)}
            variant={debugMode ? "secondary" : "outline"}
            size="sm"
          >
            Debug {debugMode ? 'ON' : 'OFF'}
          </Button>

          <Button
            onClick={() => setShowDebugOverlay(!showDebugOverlay)}
            variant={showDebugOverlay ? "secondary" : "outline"}
            size="sm"
            disabled={!debugMode}
          >
            Overlay
          </Button>
          
          <Button 
            onClick={takeSnapshot} 
            disabled={!isCameraOn || isLoading}
            className="flex-1"
          >
            <Download className="mr-2 h-4 w-4" />
            Take Snapshot
          </Button>
        </div>
        
        {/* Adjustment Controls */}
        <div className="space-y-3">
          <div>
            <label className="text-sm font-medium mb-2 block">
              Brightness: {tryOnSettings.brightness}%
            </label>
            <Slider
              value={[tryOnSettings.brightness]}
              onValueChange={([value]) => setTryOnSettings({ brightness: value })}
              min={50}
              max={150}
              step={10}
            />
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">
              Size: {Math.round(tryOnSettings.zoom * 100)}%
            </label>
            <Slider
              value={[tryOnSettings.zoom * 100]}
              onValueChange={([value]) => setTryOnSettings({ zoom: value / 100 })}
              min={50}
              max={150}
              step={5}
            />
          </div>

          {/* Position Fine-tuning */}
          <div className="border-t pt-3">
            <h4 className="text-sm font-medium mb-3">Position Adjustment</h4>

            <div className="space-y-2">
              <div>
                <label className="text-xs text-gray-600 mb-1 block">
                  Horizontal: {tryOnSettings.offsetX > 0 ? '+' : ''}{tryOnSettings.offsetX}
                </label>
                <Slider
                  value={[tryOnSettings.offsetX]}
                  onValueChange={([value]) => setTryOnSettings({ offsetX: value })}
                  min={-50}
                  max={50}
                  step={1}
                />
              </div>

              <div>
                <label className="text-xs text-gray-600 mb-1 block">
                  Vertical: {tryOnSettings.offsetY > 0 ? '+' : ''}{tryOnSettings.offsetY}
                </label>
                <Slider
                  value={[tryOnSettings.offsetY]}
                  onValueChange={([value]) => setTryOnSettings({ offsetY: value })}
                  min={-50}
                  max={50}
                  step={1}
                />
              </div>

              <div>
                <label className="text-xs text-gray-600 mb-1 block">
                  Depth: {tryOnSettings.offsetZ > 0 ? '+' : ''}{tryOnSettings.offsetZ}
                </label>
                <Slider
                  value={[tryOnSettings.offsetZ]}
                  onValueChange={([value]) => setTryOnSettings({ offsetZ: value })}
                  min={-20}
                  max={20}
                  step={1}
                />
              </div>
            </div>

            <Button
              variant="outline"
              size="sm"
              className="w-full mt-2"
              onClick={() => setTryOnSettings({ offsetX: 0, offsetY: 0, offsetZ: 0 })}
            >
              Reset Position
            </Button>
          </div>
        </div>
      </div>
      
      {/* Permission Dialog */}
      <Dialog open={showPermissionDialog} onOpenChange={setShowPermissionDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Camera Permission Required</DialogTitle>
            <DialogDescription>
              To use the virtual try-on feature, we need access to your camera. 
              Your camera feed is processed locally and never leaves your device.
              <br /><br />
              Please allow camera access when prompted by your browser.
            </DialogDescription>
          </DialogHeader>
          <div className="flex gap-2 justify-end mt-4">
            <Button variant="outline" onClick={() => setShowPermissionDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => {
              setShowPermissionDialog(false)
              startCamera()
            }}>
              Grant Permission
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  )
}