'use client'

import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { useCatalogStore } from '@/lib/store'
import { X } from 'lucide-react'

const GENDERS = ['men', 'women', 'unisex']
const FRAME_SHAPES = ['round', 'square', 'aviator', 'cat-eye', 'rectangular', 'oval']
const COLORS = [
  { name: 'Black', code: '#000000' },
  { name: '<PERSON>', code: '#8B4513' },
  { name: 'Blue', code: '#0000FF' },
  { name: 'Gold', code: '#FFD700' },
  { name: 'Silver', code: '#C0C0C0' },
  { name: 'Tortoise', code: '#D2691E' },
]

export function ProductFilters() {
  const { filters, setFilters, resetFilters } = useCatalogStore()
  const hasActiveFilters = 
    filters.gender.length > 0 || 
    filters.frameShape.length > 0 || 
    filters.color.length > 0 ||
    (filters.priceRange[0] > 0 || filters.priceRange[1] < 500)

  const toggleFilter = (type: keyof typeof filters, value: string) => {
    const currentValues = filters[type] as string[]
    const newValues = currentValues.includes(value)
      ? currentValues.filter(v => v !== value)
      : [...currentValues, value]
    
    setFilters({ [type]: newValues })
  }

  return (
    <div className="space-y-6">
      {hasActiveFilters && (
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Active Filters</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={resetFilters}
            className="h-8 px-2"
          >
            <X className="h-4 w-4 mr-1" />
            Clear All
          </Button>
        </div>
      )}

      {/* Gender Filter */}
      <div>
        <Label className="text-sm font-semibold mb-3 block">Gender</Label>
        <div className="space-y-2">
          {GENDERS.map((gender) => (
            <label key={gender} className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                className="rounded border-gray-300"
                checked={filters.gender.includes(gender)}
                onChange={() => toggleFilter('gender', gender)}
              />
              <span className="text-sm capitalize">{gender}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Frame Shape Filter */}
      <div>
        <Label className="text-sm font-semibold mb-3 block">Frame Shape</Label>
        <div className="space-y-2">
          {FRAME_SHAPES.map((shape) => (
            <label key={shape} className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                className="rounded border-gray-300"
                checked={filters.frameShape.includes(shape)}
                onChange={() => toggleFilter('frameShape', shape)}
              />
              <span className="text-sm capitalize">{shape.replace('-', ' ')}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Color Filter */}
      <div>
        <Label className="text-sm font-semibold mb-3 block">Color</Label>
        <div className="grid grid-cols-3 gap-2">
          {COLORS.map((color) => (
            <button
              key={color.name}
              className={`flex flex-col items-center p-2 border rounded-lg hover:border-primary transition-colors ${
                filters.color.includes(color.name.toLowerCase())
                  ? 'border-primary bg-primary/5'
                  : 'border-gray-200'
              }`}
              onClick={() => toggleFilter('color', color.name.toLowerCase())}
            >
              <div
                className="w-8 h-8 rounded-full border-2 border-gray-300 mb-1"
                style={{ backgroundColor: color.code }}
              />
              <span className="text-xs">{color.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Price Range Filter */}
      <div>
        <Label className="text-sm font-semibold mb-3 block">
          Price Range: ${filters.priceRange[0]} - ${filters.priceRange[1]}
        </Label>
        <Slider
          value={filters.priceRange}
          onValueChange={(value) => setFilters({ priceRange: value as [number, number] })}
          min={0}
          max={500}
          step={10}
          className="mt-2"
        />
      </div>
    </div>
  )
}