'use client'

import Link from 'next/link'
import Image from 'next/image'
import { Heart, Eye } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useWishlistStore } from '@/lib/store'
import { Database } from '@/types/database'

type Product = Database['public']['Tables']['products']['Row'] & {
  variants?: Array<Database['public']['Tables']['product_variants']['Row']>
}

interface ProductCardProps {
  product: Product
}

export function ProductCard({ product }: ProductCardProps) {
  const { items: wishlistItems, toggleItem } = useWishlistStore()
  const isInWishlist = wishlistItems.includes(product.id)
  
  // Get default variant or first variant for image
  const defaultVariant = product.variants?.find(v => v.is_default) || product.variants?.[0]
  const imageUrl = defaultVariant?.images?.[0] || '/placeholder-glasses.jpg'

  return (
    <Card className="group relative overflow-hidden hover:shadow-lg transition-all duration-300">
      <Link href={`/product/${product.slug}`}>
        <div className="aspect-[4/3] relative overflow-hidden bg-gray-100">
          <Image
            src={imageUrl}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
          {product.featured && (
            <Badge className="absolute top-2 left-2" variant="secondary">
              Featured
            </Badge>
          )}
        </div>
      </Link>
      
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-2">
          <div className="flex-1">
            <h3 className="font-semibold text-lg line-clamp-1">
              <Link href={`/product/${product.slug}`} className="hover:text-primary">
                {product.name}
              </Link>
            </h3>
            <p className="text-sm text-gray-500 capitalize">{product.gender} • {product.frame_shape}</p>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="ml-2 -mt-1"
            onClick={(e) => {
              e.preventDefault()
              toggleItem(product.id)
            }}
          >
            <Heart
              className={`h-4 w-4 ${
                isInWishlist ? 'fill-red-500 text-red-500' : ''
              }`}
            />
          </Button>
        </div>
        
        <div className="flex items-center justify-between mt-3">
          <p className="text-xl font-bold">${product.price}</p>
          {product.variants && product.variants.length > 1 && (
            <p className="text-sm text-gray-500">
              {product.variants.length} colors
            </p>
          )}
        </div>
        
        <div className="mt-3 flex gap-2">
          <Button asChild className="flex-1">
            <Link href={`/product/${product.slug}`}>
              <Eye className="mr-2 h-4 w-4" />
              View Details
            </Link>
          </Button>
          <Button variant="outline" asChild className="flex-1">
            <Link href={`/try-on/${product.id}`}>
              Try On
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}