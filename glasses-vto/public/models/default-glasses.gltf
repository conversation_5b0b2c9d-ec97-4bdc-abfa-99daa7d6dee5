{"accessors": [{"bufferView": 2, "componentType": 5126, "count": 9204, "max": [80.99517822265625, 167.78591918945312, 4.012095928192139], "min": [-83.78717041015625, 22.925212860107422, -20.808706283569336], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 110448, "componentType": 5126, "count": 9204, "max": [0.9999991059303284, 0.9999486207962036, 1.0], "min": [-0.9999812245368958, -0.999093770980835, -1.0], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 9204, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "componentType": 5125, "count": 36015, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 220896, "componentType": 5126, "count": 3517, "max": [80.17152404785156, 161.8310546875, 7.094304084777832], "min": [-65.72896575927734, 5.1300530433654785, -31.170045852661133], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 263100, "componentType": 5126, "count": 3517, "max": [0.9865427613258362, 0.9991667866706848, 0.9965958595275879], "min": [-0.9994456171989441, -0.9921799898147583, -0.997552752494812], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 73632, "componentType": 5126, "count": 3517, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 144060, "componentType": 5125, "count": 13131, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 305304, "componentType": 5126, "count": 28987, "max": [74.1229476928711, 35.103614807128906, 7.269126892089844], "min": [-73.69610595703125, 1.9121259450912476, -33.284912109375], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 653148, "componentType": 5126, "count": 28987, "max": [0.999978244304657, 0.9999998807907104, 1.0], "min": [-0.9999781250953674, -0.9999252557754517, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 101768, "componentType": 5126, "count": 28987, "max": [0.9985349774360657, 0.9828429818153381], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 196584, "componentType": 5125, "count": 107805, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1000992, "componentType": 5126, "count": 9390, "max": [83.48058319091797, 167.5619659423828, 4.012095928192139], "min": [67.24713897705078, 27.333959579467773, -20.808956146240234], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1113672, "componentType": 5126, "count": 9390, "max": [0.9999881386756897, 0.9999731183052063, 1.0], "min": [-0.9999979734420776, -0.9999918937683105, -1.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 333664, "componentType": 5126, "count": 9390, "max": [1.0, 1.0], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 627804, "componentType": 5125, "count": 36216, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1226352, "componentType": 5126, "count": 2538, "max": [-69.38640594482422, 162.0625457763672, -4.353198051452637], "min": [-81.35480499267578, 103.54927825927734, -18.23145866394043], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1256808, "componentType": 5126, "count": 2538, "max": [0.9994456171989441, 0.9953683614730835, 0.9991453289985657], "min": [-0.9999600052833557, -0.9958757758140564, -0.9992008805274963], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 408784, "componentType": 5126, "count": 2538, "max": [0.9985349774360657, 0.9985349774360657], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 772668, "componentType": 5125, "count": 10044, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1287264, "componentType": 5126, "count": 760, "max": [79.97198486328125, 161.82826232910156, -4.426196098327637], "min": [-80.321533203125, 103.65180206298828, -18.079833984375], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1296384, "componentType": 5126, "count": 760, "max": [0.9715375304222107, -0.09471902251243591, 0.7923531532287598], "min": [-0.9715376496315002, -0.5219401121139526, -0.5220620632171631], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 429088, "componentType": 5126, "count": 760, "max": [0.1306850016117096, 0.9463840126991272], "min": [0.01434400025755167, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 812844, "componentType": 5125, "count": 3138, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1305504, "componentType": 5126, "count": 4590, "max": [76.93748474121094, 45.17991638183594, 0.3409020006656647], "min": [-76.61778259277344, 37.31940841674805, -6.494949817657471], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1360584, "componentType": 5126, "count": 4590, "max": [0.999937891960144, 0.9979963302612305, 0.9999607801437378], "min": [-0.9999719858169556, -0.9997632503509521, -0.9999620914459229], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 435168, "componentType": 5126, "count": 4590, "max": [1.0, 0.9985349774360657], "min": [0.0, 0.0], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 825396, "componentType": 5125, "count": 15678, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1415664, "componentType": 5126, "count": 1565, "max": [62.65596008300781, 18.794849395751953, 4.244718074798584], "min": [-62.65351104736328, 5.245065212249756, -29.99279022216797], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1434444, "componentType": 5126, "count": 1565, "max": [0.4670450687408447, 0.9994712471961975, 0.25697699189186096], "min": [-0.4675500690937042, -0.99947190284729, -0.2568700909614563], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 471888, "componentType": 5126, "count": 1565, "max": [0.9985349774360657, 0.8581129908561707], "min": [0.10576300323009491, 0.09996800124645233], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 888108, "componentType": 5125, "count": 6012, "type": "SCALAR"}], "asset": {"extras": {"author": "hanchiahui (https://sketchfab.com/hanchiahui)", "license": "CC-BY-4.0 (http://creativecommons.org/licenses/by/4.0/)", "source": "https://sketchfab.com/3d-models/sport-glasses-b307-7630c4ac090c42598de43d47554b4cf4", "title": "Sport Glasses B307"}, "generator": "Sketchfab-12.66.0", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 912156, "name": "floatBufferViews", "target": 34963}, {"buffer": 0, "byteLength": 484408, "byteOffset": 912156, "byteStride": 8, "name": "floatBufferViews", "target": 34962}, {"buffer": 0, "byteLength": 1453224, "byteOffset": 1396564, "byteStride": 12, "name": "floatBufferViews", "target": 34962}], "buffers": [{"byteLength": 2849788, "uri": "scene.bin"}], "extensionsUsed": ["KHR_materials_transmission"], "images": [{"uri": "textures/B307_REALlambert3SG_baseColor.png"}, {"uri": "textures/B307_REALphong1SG2_metallicRoughness.png"}, {"uri": "textures/B307_REALphong1SG2_emissive.jpeg"}, {"uri": "textures/B307_REALphong1SG2_transmission.png"}], "materials": [{"doubleSided": true, "name": "B307_REALlambert2SG", "pbrMetallicRoughness": {"baseColorFactor": [0.92, 0.92, 0.92, 1.0], "metallicFactor": 0.0}}, {"alphaMode": "BLEND", "doubleSided": true, "name": "B307_REALlambert3SG", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0.0}}, {"doubleSided": true, "name": "B307_REALphong1SG", "pbrMetallicRoughness": {"baseColorFactor": [0.94, 0.73, 0.2, 1.0], "roughnessFactor": 0.8970116428}}, {"alphaMode": "BLEND", "emissiveFactor": [0.49726254391916797, 0.49726254391916797, 0.49726254391916797], "emissiveTexture": {"index": 3}, "extensions": {"KHR_materials_transmission": {"transmissionFactor": 1.0, "transmissionTexture": {"index": 4}}}, "name": "B307_REALphong1SG2", "pbrMetallicRoughness": {"baseColorTexture": {"index": 1}, "metallicFactor": 0.1537957615205965, "metallicRoughnessTexture": {"index": 2}, "roughnessFactor": 0.7281937735234784}}], "meshes": [{"name": "Object_0", "primitives": [{"attributes": {"NORMAL": 1, "POSITION": 0, "TEXCOORD_0": 2}, "indices": 3, "material": 0, "mode": 4}]}, {"name": "Object_1", "primitives": [{"attributes": {"NORMAL": 5, "POSITION": 4, "TEXCOORD_0": 6}, "indices": 7, "material": 0, "mode": 4}]}, {"name": "Object_2", "primitives": [{"attributes": {"NORMAL": 9, "POSITION": 8, "TEXCOORD_0": 10}, "indices": 11, "material": 0, "mode": 4}]}, {"name": "Object_3", "primitives": [{"attributes": {"NORMAL": 13, "POSITION": 12, "TEXCOORD_0": 14}, "indices": 15, "material": 0, "mode": 4}]}, {"name": "Object_4", "primitives": [{"attributes": {"NORMAL": 17, "POSITION": 16, "TEXCOORD_0": 18}, "indices": 19, "material": 0, "mode": 4}]}, {"name": "Object_5", "primitives": [{"attributes": {"NORMAL": 21, "POSITION": 20, "TEXCOORD_0": 22}, "indices": 23, "material": 1, "mode": 4}]}, {"name": "Object_6", "primitives": [{"attributes": {"NORMAL": 25, "POSITION": 24, "TEXCOORD_0": 26}, "indices": 27, "material": 2, "mode": 4}]}, {"name": "Object_7", "primitives": [{"attributes": {"NORMAL": 29, "POSITION": 28, "TEXCOORD_0": 30}, "indices": 31, "material": 3, "mode": 4}]}], "nodes": [{"children": [1], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 2.220446049250313e-16, -1.0, 0.0, 0.0, 1.0, 2.220446049250313e-16, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Sketchfab_model"}, {"children": [2, 3, 4, 5, 6, 7, 8, 9], "name": "B307_REAL.obj.cleaner.materialmerger.gles"}, {"mesh": 0, "name": "Object_2"}, {"mesh": 1, "name": "Object_3"}, {"mesh": 2, "name": "Object_4"}, {"mesh": 3, "name": "Object_5"}, {"mesh": 4, "name": "Object_6"}, {"mesh": 5, "name": "Object_7"}, {"mesh": 6, "name": "Object_8"}, {"mesh": 7, "name": "Object_9"}], "samplers": [{"magFilter": 9729, "minFilter": 9987, "wrapS": 10497, "wrapT": 10497}, {"magFilter": 9728, "minFilter": 9986, "wrapS": 33648, "wrapT": 10497}], "scene": 0, "scenes": [{"name": "Sketchfab_Scene", "nodes": [0]}], "textures": [{"sampler": 0, "source": 0}, {"sampler": 1, "source": 0}, {"sampler": 0, "source": 1}, {"sampler": 0, "source": 2}, {"sampler": 0, "source": 3}]}