// Test script to verify Supabase connection
// Using built-in fetch (Node.js 18+)

async function testConnection() {
  console.log('Testing Supabase connection...\n');
  
  try {
    // Test products endpoint
    console.log('1. Testing /api/products endpoint:');
    const productsResponse = await fetch('http://localhost:3000/api/products');
    const products = await productsResponse.json();
    console.log(`   ✓ Found ${products.length} products`);
    if (products.length > 0) {
      console.log(`   First product: ${products[0].name} (${products[0].brand})`);
    }
    
    // Test categories endpoint
    console.log('\n2. Testing /api/categories endpoint:');
    const categoriesResponse = await fetch('http://localhost:3000/api/categories');
    const categories = await categoriesResponse.json();
    console.log(`   ✓ Found ${categories.length} categories`);
    if (categories.length > 0) {
      console.log(`   Categories: ${categories.map(c => c.name).join(', ')}`);
    }
    
    console.log('\n✅ Backend integration is working correctly!');
    console.log('\nYou can now visit:');
    console.log('- http://localhost:3000/catalog to see the products');
    console.log('- http://localhost:3000/api/products to see raw API data');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.log('\nPlease make sure:');
    console.log('1. The Next.js dev server is running (npm run dev)');
    console.log('2. Supabase credentials are correctly set in .env.local');
    console.log('3. Database schema and seed data have been applied in Supabase');
  }
}

testConnection();