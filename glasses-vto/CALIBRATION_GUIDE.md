# 🎯 Virtual Try-On Calibration Guide

## Overview
This guide explains how to calibrate the virtual try-on system for optimal glasses positioning and accuracy.

## 🔧 Positioning Improvements Made

### 1. **Enhanced Face Detection**
- **Better Eye Center Calculation**: Uses more accurate MediaPipe landmarks for eye positioning
- **Nose Bridge Reference**: Positions glasses relative to nose bridge instead of just eye centers
- **Multi-Point Averaging**: Calculates eye centers using multiple landmark points for stability

### 2. **Improved 3D Positioning**
- **Aspect Ratio Handling**: Properly handles different video aspect ratios
- **Depth Positioning**: Uses Z-coordinate from face landmarks for better depth placement
- **Weighted Positioning**: Combines eye center and nose bridge positions for optimal placement

### 3. **Advanced Scaling Algorithm**
- **Interpupillary Distance**: Uses eye distance for primary scaling
- **Face Width Reference**: Secondary scaling based on face width
- **Clamped Range**: Prevents extreme scaling (0.5x to 2.0x)

### 4. **Snapshot Consistency**
- **Proper Mirroring**: Ensures snapshot matches display view
- **High Quality Export**: Uses 95% quality PNG export
- **Consistent Scaling**: Maintains same positioning in snapshots

## 🎮 User Controls

### Basic Adjustments
- **Brightness**: 50% - 150% (affects glasses visibility)
- **Size**: 50% - 150% (overall glasses scale)

### Fine-Tuning Controls
- **Horizontal Position**: -50 to +50 (left/right adjustment)
- **Vertical Position**: -50 to +50 (up/down adjustment)  
- **Depth Position**: -20 to +20 (forward/backward adjustment)

## 📐 Technical Details

### MediaPipe Landmarks Used
```
Eye Centers: 468, 473 (calculated from multiple points)
Eye Corners: 33, 263 (outer), 133, 362 (inner)
Nose Bridge: 6 (top), 168 (center)
Face Outline: 10 (forehead), 152 (chin)
Temples: 54, 284
Cheeks: 234, 454
```

### Coordinate Conversion
```javascript
// MediaPipe (0-1 normalized) → Three.js world coordinates
worldX = (normalizedX - 0.5) * 6 * aspectRatio + offsetX
worldY = -(normalizedY - 0.5) * 6 + offsetY  // Y inverted
worldZ = normalizedZ * 1.5 + offsetZ
```

### Positioning Algorithm
1. **Calculate eye centers** from multiple landmark points
2. **Find nose bridge position** for vertical reference
3. **Blend positions** (60% eye center + 40% nose bridge)
4. **Apply aspect ratio correction**
5. **Add user offsets** for fine-tuning
6. **Apply rotation and scaling**

## 🚀 Usage Tips

### For Best Results:
1. **Good Lighting**: Ensure face is well-lit and evenly illuminated
2. **Face Position**: Keep face centered and at comfortable distance
3. **Stable Position**: Minimize head movement during calibration
4. **Camera Quality**: Use highest resolution camera available

### Calibration Process:
1. Start with default settings
2. Adjust **Size** first to match face proportions
3. Use **Vertical Position** to align with nose bridge
4. Fine-tune **Horizontal Position** for centering
5. Adjust **Depth** if glasses appear too close/far
6. Test with different head angles and positions

### Common Issues & Solutions:

**Glasses too high/low:**
- Adjust Vertical Position slider
- Check lighting on nose area

**Glasses too wide/narrow:**
- Adjust Size slider
- Ensure both eyes are clearly visible

**Glasses appear tilted:**
- Keep head level during detection
- Ensure good lighting on both sides of face

**Snapshot different from display:**
- Fixed in latest version with proper mirroring
- Ensure camera permissions are granted

## 🔄 Reset Options

- **Reset Position**: Resets all offset values to 0
- **Page Refresh**: Resets all settings to defaults
- **Clear Browser Data**: Removes all saved preferences

## 📊 Performance Optimization

The system now includes:
- **Efficient Landmark Processing**: Reduced computation overhead
- **Smooth Interpolation**: Prevents jittery movements
- **Adaptive Quality**: Adjusts rendering based on device capabilities
- **Memory Management**: Proper cleanup of resources

## 🐛 Troubleshooting

### Face Not Detected:
- Check camera permissions
- Ensure adequate lighting
- Move closer to camera
- Remove obstructions (hands, hair)

### Poor Positioning:
- Calibrate using fine-tuning controls
- Check MediaPipe model loading
- Verify 3D model is loaded correctly

### Performance Issues:
- Close other browser tabs
- Ensure good internet connection
- Try different browser (Chrome recommended)
- Check device specifications

## 📝 Technical Notes

- **MediaPipe Version**: Latest face_landmarker model
- **Three.js Rendering**: WebGL with alpha blending
- **Coordinate System**: Right-handed with Y-up
- **Model Format**: GLB/GLTF with PBR materials
- **Browser Support**: Chrome, Firefox, Safari, Edge

---

*For technical support or feature requests, please refer to the project documentation.*
