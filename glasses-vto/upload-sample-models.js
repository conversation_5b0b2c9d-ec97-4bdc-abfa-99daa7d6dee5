const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials in .env.local')
  process.exit(1)
}

const adminSupabase = createClient(supabaseUrl, supabaseServiceKey)

async function uploadSampleModels() {
  try {
    console.log('🚀 Starting sample 3D models upload...')
    
    // Path to sample models
    const modelsDir = path.join(__dirname, '..', '3dmodel', 'glasses-03')
    
    if (!fs.existsSync(modelsDir)) {
      console.error('❌ Sample models directory not found:', modelsDir)
      return
    }
    
    console.log('📁 Found models directory:', modelsDir)
    
    // List files in the directory
    const files = fs.readdirSync(modelsDir, { withFileTypes: true })
    console.log('📋 Files found:')
    files.forEach(file => {
      console.log(`  - ${file.name} (${file.isDirectory() ? 'directory' : 'file'})`)
    })
    
    // Upload GLTF file
    const gltfFile = path.join(modelsDir, 'scene.gltf')
    const binFile = path.join(modelsDir, 'scene.bin')
    const previewImage = path.join(modelsDir, 'glasses_03.png')
    
    if (fs.existsSync(gltfFile)) {
      console.log('\n📦 Uploading GLTF model...')
      
      // Read GLTF file
      const gltfBuffer = fs.readFileSync(gltfFile)
      
      // Upload GLTF to models bucket
      const gltfPath = 'sample-glasses-03/black/model.gltf'
      const { data: gltfUpload, error: gltfError } = await adminSupabase.storage
        .from('models')
        .upload(gltfPath, gltfBuffer, {
          contentType: 'model/gltf+json',
          upsert: true
        })
      
      if (gltfError) {
        console.error('❌ GLTF upload failed:', gltfError.message)
      } else {
        console.log('✅ GLTF uploaded:', gltfUpload.path)
      }
      
      // Upload BIN file if exists
      if (fs.existsSync(binFile)) {
        console.log('📦 Uploading BIN file...')
        const binBuffer = fs.readFileSync(binFile)
        const binPath = 'sample-glasses-03/black/scene.bin'
        
        const { data: binUpload, error: binError } = await adminSupabase.storage
          .from('models')
          .upload(binPath, binBuffer, {
            contentType: 'application/octet-stream',
            upsert: true
          })
        
        if (binError) {
          console.error('❌ BIN upload failed:', binError.message)
        } else {
          console.log('✅ BIN uploaded:', binUpload.path)
        }
      }
      
      // Upload textures
      const texturesDir = path.join(modelsDir, 'textures')
      if (fs.existsSync(texturesDir)) {
        console.log('🎨 Uploading textures...')
        const textureFiles = fs.readdirSync(texturesDir)
        
        for (const textureFile of textureFiles) {
          const texturePath = path.join(texturesDir, textureFile)
          const textureBuffer = fs.readFileSync(texturePath)
          const uploadPath = `sample-glasses-03/black/textures/${textureFile}`
          
          const { data: textureUpload, error: textureError } = await adminSupabase.storage
            .from('models')
            .upload(uploadPath, textureBuffer, {
              contentType: 'image/png',
              upsert: true
            })
          
          if (textureError) {
            console.error(`❌ Texture upload failed (${textureFile}):`, textureError.message)
          } else {
            console.log(`✅ Texture uploaded: ${textureUpload.path}`)
          }
        }
      }
    }
    
    // Upload preview image
    if (fs.existsSync(previewImage)) {
      console.log('\n🖼️ Uploading preview image...')
      const imageBuffer = fs.readFileSync(previewImage)
      const imagePath = 'sample-glasses-03/black/preview.png'
      
      const { data: imageUpload, error: imageError } = await adminSupabase.storage
        .from('products')
        .upload(imagePath, imageBuffer, {
          contentType: 'image/png',
          upsert: true
        })
      
      if (imageError) {
        console.error('❌ Image upload failed:', imageError.message)
      } else {
        console.log('✅ Preview image uploaded:', imageUpload.path)
      }
    }
    
    // Create sample product
    console.log('\n🏷️ Creating sample product...')
    const { data: product, error: productError } = await adminSupabase
      .from('products')
      .insert({
        slug: 'sample-glasses-03',
        name: 'Sample Glasses 03',
        description: 'Sample 3D glasses for virtual try-on testing',
        brand: 'Sample Brand',
        price_cents: 19900
      })
      .select()
      .single()
    
    if (productError) {
      console.error('❌ Product creation failed:', productError.message)
    } else {
      console.log('✅ Product created:', product.name, '(ID:', product.id + ')')
      
      // Create product variant
      console.log('🎨 Creating product variant...')
      const { data: variant, error: variantError } = await adminSupabase
        .from('product_variants')
        .insert({
          product_id: product.id,
          sku: 'SAMPLE-03-BLK-M',
          color: 'black',
          size: 'medium',
          bridge_width_mm: 18,
          temple_length_mm: 140,
          lens_width_mm: 52,
          stock: 10,
          glb_path: 'sample-glasses-03/black/model.gltf',
          preview_image_path: 'sample-glasses-03/black/preview.png'
        })
        .select()
        .single()
      
      if (variantError) {
        console.error('❌ Variant creation failed:', variantError.message)
      } else {
        console.log('✅ Variant created:', variant.color, variant.size)
      }
      
      // Add to categories
      console.log('📂 Adding to categories...')
      const { data: categories } = await adminSupabase
        .from('categories')
        .select('id, slug')
        .in('slug', ['sunglasses', 'luxury'])
      
      if (categories && categories.length > 0) {
        const categoryRelations = categories.map(cat => ({
          product_id: product.id,
          category_id: cat.id
        }))
        
        const { error: categoryError } = await adminSupabase
          .from('product_categories')
          .insert(categoryRelations)
        
        if (categoryError) {
          console.error('❌ Category relations failed:', categoryError.message)
        } else {
          console.log('✅ Added to categories:', categories.map(c => c.slug).join(', '))
        }
      }
    }
    
    console.log('\n🎉 Sample models upload completed!')
    console.log('📋 Summary:')
    console.log('  - GLTF model uploaded to models bucket')
    console.log('  - Preview image uploaded to products bucket')
    console.log('  - Sample product created with variant')
    console.log('  - Ready for virtual try-on testing')
    
  } catch (error) {
    console.error('❌ Upload failed:', error.message)
  }
}

// Run the upload
if (require.main === module) {
  uploadSampleModels()
    .then(() => process.exit(0))
    .catch(() => process.exit(1))
}

module.exports = { uploadSampleModels }
