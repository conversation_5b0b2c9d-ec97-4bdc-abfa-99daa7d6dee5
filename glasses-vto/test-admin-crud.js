const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceKey) {
  console.error('Missing Supabase credentials in .env.local')
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, NEXT_PUBLIC_SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)
const adminSupabase = createClient(supabaseUrl, supabaseServiceKey)

async function testAdminCRUD() {
  try {
    console.log('🧪 Testing Admin CRUD with Service Role...')
    
    // Test 1: Create product with admin client
    console.log('\n📦 Testing product creation with admin client...')
    const { data: product, error: productError } = await adminSupabase
      .from('products')
      .insert({
        slug: 'admin-test-glasses-' + Date.now(),
        name: 'Admin Test Glasses',
        description: 'Test glasses created via admin client',
        brand: 'Admin Brand',
        price_cents: 30000
      })
      .select()
      .single()
    
    if (productError) {
      console.error('❌ Product creation failed:', productError.message)
    } else {
      console.log('✅ Product created successfully:', product.name)
      console.log('   ID:', product.id)
      
      // Test 2: Update product
      console.log('\n✏️ Testing product update...')
      const { data: updatedProduct, error: updateError } = await adminSupabase
        .from('products')
        .update({ name: 'Updated Admin Test Glasses' })
        .eq('id', product.id)
        .select()
        .single()
      
      if (updateError) {
        console.error('❌ Product update failed:', updateError.message)
      } else {
        console.log('✅ Product updated successfully:', updatedProduct.name)
      }
      
      // Test 3: Delete product
      console.log('\n🗑️ Testing product deletion...')
      const { error: deleteError } = await adminSupabase
        .from('products')
        .delete()
        .eq('id', product.id)
      
      if (deleteError) {
        console.error('❌ Product deletion failed:', deleteError.message)
      } else {
        console.log('✅ Product deleted successfully')
      }
    }
    
    // Test 4: Create category with admin client
    console.log('\n🏷️ Testing category creation with admin client...')
    const { data: category, error: categoryError } = await adminSupabase
      .from('categories')
      .insert({
        slug: 'admin-test-category-' + Date.now(),
        name: 'Admin Test Category'
      })
      .select()
      .single()
    
    if (categoryError) {
      console.error('❌ Category creation failed:', categoryError.message)
    } else {
      console.log('✅ Category created successfully:', category.name)
      
      // Clean up category
      await adminSupabase.from('categories').delete().eq('id', category.id)
      console.log('✅ Category cleaned up')
    }
    
    // Test 5: Test admin API endpoints
    console.log('\n🌐 Testing admin API endpoints...')
    
    // First, sign in as admin
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    })
    
    if (authError) {
      console.error('❌ Auth error:', authError.message)
      return
    }
    
    console.log('✅ Signed in as:', authData.user.email)
    
    // Get session token for API calls
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session) {
      console.error('❌ No session found')
      return
    }
    
    // Test API endpoint for creating product
    console.log('\n📡 Testing POST /api/admin/products...')
    const response = await fetch('http://localhost:3000/api/admin/products', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        slug: 'api-test-glasses-' + Date.now(),
        name: 'API Test Glasses',
        description: 'Test glasses created via API',
        brand: 'API Brand',
        price_cents: 25000
      })
    })
    
    if (response.ok) {
      const apiProduct = await response.json()
      console.log('✅ API product creation successful:', apiProduct.name)
      
      // Clean up
      await adminSupabase.from('products').delete().eq('id', apiProduct.id)
      console.log('✅ API product cleaned up')
    } else {
      const error = await response.text()
      console.error('❌ API product creation failed:', error)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

// Run the test
if (require.main === module) {
  console.log('🚀 Starting Admin CRUD Test...')
  
  testAdminCRUD()
    .then(() => {
      console.log('\n📋 Test Summary:')
      console.log('- Admin client (service role) should bypass RLS')
      console.log('- API endpoints should work with authenticated users')
      console.log('- If tests pass, admin panel CRUD should work')
    })
    .then(() => process.exit(0))
    .catch(() => process.exit(1))
}

module.exports = { testAdminCRUD }
