{"accessors": [{"bufferView": 2, "componentType": 5126, "count": 65532, "max": [7.641334056854248, 10.587218284606934, 2.2955551147460938], "min": [-6.805859088897705, -1.8556480407714844, -0.6658750176429749], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 786384, "componentType": 5126, "count": 65532, "max": [0.9999855756759644, 0.9996947646141052, 0.9999982118606567], "min": [-0.9999856948852539, -0.9996947646141052, -0.9999669790267944], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 65532, "max": [0.999800980091095, 0.9045550227165222], "min": [0.28248199820518494, 0.00023999999393709004], "type": "VEC2"}, {"bufferView": 0, "componentType": 5125, "count": 98298, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1572768, "componentType": 5126, "count": 1028, "max": [-0.1705709993839264, -1.3346480131149292, 0.311693012714386], "min": [-0.44162899255752563, -1.6727550029754639, -0.6658750176429749], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1585104, "componentType": 5126, "count": 1028, "max": [0.7332935929298401, 0.1012018695473671, 0.9773752093315125], "min": [-0.9835334420204163, -0.9974042773246765, -0.9475367069244385], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 524256, "componentType": 5126, "count": 1028, "max": [0.6209250092506409, 0.5886380076408386], "min": [0.5467820167541504, 0.5709869861602783], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 393192, "componentType": 5125, "count": 1542, "type": "SCALAR"}, {"bufferView": 2, "byteOffset": 1597440, "componentType": 5126, "count": 18432, "max": [7.7519378662109375, -1.40420401096344, 2.091836929321289], "min": [-6.9164628982543945, -1.898813009262085, -1.958055019378662], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 1818624, "componentType": 5126, "count": 18432, "max": [0.8965833187103271, 0.999991**********, 0.9394309520721436], "min": [-0.8965833187103271, -0.9999938011169434, -0.9356544017791748], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 532480, "componentType": 5126, "count": 18432, "max": [0.5367339849472046, 0.7699329853057861], "min": [0.0008099999977275729, 0.0007980000227689743], "type": "VEC2"}, {"bufferView": 0, "byteOffset": 399360, "componentType": 5125, "count": 27648, "type": "SCALAR"}], "asset": {"extras": {"author": "thelegendofwolf (https://sketchfab.com/thelegendofwolf)", "license": "CC-BY-4.0 (http://creativecommons.org/licenses/by/4.0/)", "source": "https://sketchfab.com/3d-models/eyeglasses-8ec54755399a4eca8a1356812e68fe02", "title": "EyeGlasses"}, "generator": "Sketchfab-12.68.0", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 509952, "name": "floatBufferViews", "target": 34963}, {"buffer": 0, "byteLength": 679936, "byteOffset": 509952, "byteStride": 8, "name": "floatBufferViews", "target": 34962}, {"buffer": 0, "byteLength": 2039808, "byteOffset": 1189888, "byteStride": 12, "name": "floatBufferViews", "target": 34962}], "buffers": [{"byteLength": 3229696, "uri": "scene.bin"}], "extensionsUsed": ["KHR_materials_transmission"], "images": [{"uri": "textures/Material.001_baseColor.png"}], "materials": [{"doubleSided": true, "name": "Material.001", "pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0.**********, "roughnessFactor": 0.6}}, {"alphaMode": "BLEND", "doubleSided": true, "extensions": {"KHR_materials_transmission": {"transmissionFactor": 0.**********}}, "name": "Material.003", "pbrMetallicRoughness": {"baseColorFactor": [1.0, 1.0, 1.0, 0.25], "baseColorTexture": {"index": 0}, "metallicFactor": 0.0, "roughnessFactor": 0.0}}], "meshes": [{"name": "Object_0", "primitives": [{"attributes": {"NORMAL": 1, "POSITION": 0, "TEXCOORD_0": 2}, "indices": 3, "material": 0, "mode": 4}]}, {"name": "Object_1", "primitives": [{"attributes": {"NORMAL": 5, "POSITION": 4, "TEXCOORD_0": 6}, "indices": 7, "material": 0, "mode": 4}]}, {"name": "Object_2", "primitives": [{"attributes": {"NORMAL": 9, "POSITION": 8, "TEXCOORD_0": 10}, "indices": 11, "material": 1, "mode": 4}]}], "nodes": [{"children": [1], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 2.220446049250313e-16, -1.0, 0.0, 0.0, 1.0, 2.220446049250313e-16, 0.0, 0.0, 0.0, 0.0, 1.0], "name": "Sketchfab_model"}, {"children": [2, 3, 4], "name": "briluv.obj.cleaner.materialmerger.gles"}, {"mesh": 0, "name": "Object_2"}, {"mesh": 1, "name": "Object_3"}, {"mesh": 2, "name": "Object_4"}], "samplers": [{"magFilter": 9729, "minFilter": 9987, "wrapS": 10497, "wrapT": 10497}], "scene": 0, "scenes": [{"name": "Sketchfab_Scene", "nodes": [0]}], "textures": [{"sampler": 0, "source": 0}]}