---
type: "agent_requested"
description: "Example description"
---

Whenever you install something:

First, check the latest documentation using context7mcp.

Always pick the latest version available.

Make sure the new version is compatible with the packages and tools already installed.

If there’s a conflict, then use the last stable versions that are known to work together.

Keep track of all installed versions in a changelog.