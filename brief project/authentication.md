
# Authentication & Authorization (Supabase Auth + RLS)

## Goals
- Otentikasi pengguna (Email/Password + OAuth opsional: Google/Apple).
- Otorisasi data via **RLS** di Postgres.

---
## Alur Auth
1. **Sign Up**: email + password → verifikasi email (optional).
2. **Sign In**: session JWT tersimpan (httpOnly cookie via Next.js server actions atau client).
3. **Session Handling**:
   - Server Components: gunakan `@supabase/auth-helpers-nextjs` untuk `createServerComponentClient`.
   - Client Components: `createClientComponentClient` untuk interaksi realtime.
4. **Protected Routes**: `/account`, wishlist actions → redirect bila belum login.

---
## Konfigurasi di Next.js
- Install helpers: `@supabase/auth-helpers-nextjs`.
- Buat util `lib/supabase/server.ts` & `lib/supabase/client.ts`.
- Gunakan **middleware** untuk guard halaman tertentu (opsional).

---
## UI Auth
- **Opsi 1**: Pakai `@supabase/auth-ui-react` untuk cepat.
- **Opsi 2**: Form custom (shadcn/ui) + validasi Zod.

---
## RLS Ringkasan
- Tabel publik (katalog): **read-only publik**.
- Tabel user-scope (wishlist, tryon_events): per user (owner) saja.
- Operasi admin: gunakan **service role key** (server-only) atau **Edge Functions**.

---
## Token & Keamanan
- Simpan **anon key** di client (read publik) + gunakan session JWT untuk write user.
- Admin write (produk/varian) harus lewat server (service role) → jangan bocorkan di client.
- Enforce HTTPS untuk akses kamera; tampilkan consent + privacy note.

---
## Acceptance Criteria
- Login/Logout stabil di SSR & CSR.
- Aksi wishlist hanya untuk user terautentikasi (divalidasi oleh RLS).
- Tidak ada service role di client bundle.
