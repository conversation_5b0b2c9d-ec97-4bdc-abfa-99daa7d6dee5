
# Database Design (Postgres / Supabase)

## Entity Diagram (ringkas)
- `products` (1) — (N) `product_variants` — (N) `variant_assets`
- `products` (N) — (N) `categories` via `product_categories`
- `users` (auth) (1) — (N) `wishlists`
- `users` (1) — (N) `tryon_events`

---
## Tables (DDL)
```sql
-- PRODUCTS
create table public.products (
  id uuid primary key default gen_random_uuid(),
  slug text unique not null,
  name text not null,
  description text,
  brand text,
  price_cents int not null check (price_cents >= 0),
  active boolean not null default true,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);
create index on public.products (active);
create index on public.products (slug);

-- VARIANTS (warna/ukuran/lens option)
create table public.product_variants (
  id uuid primary key default gen_random_uuid(),
  product_id uuid not null references public.products(id) on delete cascade,
  sku text unique,
  color text,
  size text,
  bridge_width_mm int,
  temple_length_mm int,
  lens_width_mm int,
  stock int default 0,
  glb_path text,            -- storage path (models bucket)
  preview_image_path text,  -- storage path (products bucket)
  active boolean not null default true,
  created_at timestamptz not null default now()
);
create index on public.product_variants (product_id);
create index on public.product_variants (active);

-- VARIANT ASSETS (tambahan tekstur/material)
create table public.variant_assets (
  id uuid primary key default gen_random_uuid(),
  variant_id uuid not null references public.product_variants(id) on delete cascade,
  kind text not null check (kind in ('texture','thumbnail','ar-mask','extra')),
  path text not null,   -- storage path
  meta jsonb default '{}'::jsonb
);
create index on public.variant_assets (variant_id);

-- CATEGORIES
create table public.categories (
  id uuid primary key default gen_random_uuid(),
  slug text unique not null,
  name text not null
);

create table public.product_categories (
  product_id uuid references public.products(id) on delete cascade,
  category_id uuid references public.categories(id) on delete cascade,
  primary key (product_id, category_id)
);

-- WISHLIST
create table public.wishlists (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  product_id uuid not null references public.products(id) on delete cascade,
  created_at timestamptz not null default now(),
  unique (user_id, product_id)
);
create index on public.wishlists (user_id);

-- TRY-ON EVENTS (telemetry ringan)
create table public.tryon_events (
  id bigint generated by default as identity primary key,
  user_id uuid references auth.users(id) on delete set null,
  product_id uuid references public.products(id) on delete set null,
  variant_id uuid references public.product_variants(id) on delete set null,
  device text,
  fps numeric,
  width int,
  height int,
  created_at timestamptz not null default now()
);
create index on public.tryon_events (created_at);
```

---
## Row Level Security (RLS) & Policies
```sql
-- Enable RLS
alter table public.wishlists enable row level security;
alter table public.tryon_events enable row level security;
alter table public.products enable row level security;
alter table public.product_variants enable row level security;
alter table public.variant_assets enable row level security;
alter table public.categories enable row level security;
alter table public.product_categories enable row level security;

-- Public read-only for catalog tables
create policy "public read products" on public.products
  for select using (true);
create policy "public read variants" on public.product_variants
  for select using (true);
create policy "public read variant_assets" on public.variant_assets
  for select using (true);
create policy "public read categories" on public.categories
  for select using (true);
create policy "public read product_categories" on public.product_categories
  for select using (true);

-- Wishlist: only owner can read/write
create policy "wishlist select own" on public.wishlists
  for select using (auth.uid() = user_id);
create policy "wishlist insert own" on public.wishlists
  for insert with check (auth.uid() = user_id);
create policy "wishlist delete own" on public.wishlists
  for delete using (auth.uid() = user_id);

-- Try-on events: insert by logged-in (optional anonymous via anon key if desired)
create policy "tryon insert own or anonymous" on public.tryon_events
  for insert with check (auth.uid() = user_id or user_id is null);

-- Optional: restrict updates on catalog to service role only (no public policy for write)
```

---
## RPC (SQL Functions)
```sql
-- Toggle wishlist atomically
create or replace function public.toggle_wishlist(p_product_id uuid)
returns void language plpgsql as $$
begin
  if exists (
    select 1 from public.wishlists
    where user_id = auth.uid() and product_id = p_product_id
  ) then
    delete from public.wishlists
    where user_id = auth.uid() and product_id = p_product_id;
  else
    insert into public.wishlists (user_id, product_id)
    values (auth.uid(), p_product_id);
  end if;
end; $$;

-- Get catalog with filters
create or replace function public.get_catalog(
  p_search text default null,
  p_category_slug text default null,
  p_color text default null,
  p_price_min int default null,
  p_price_max int default null,
  p_limit int default 24,
  p_offset int default 0
)
returns setof products
language sql stable as $$
  select * from public.products p
  where p.active
    and (p_search is null or p.name ilike '%'||p_search||'%' or p.description ilike '%'||p_search||'%')
    and (p_category_slug is null or exists (
      select 1 from public.product_categories pc
      join public.categories c on c.id = pc.category_id
      where pc.product_id = p.id and c.slug = p_category_slug
    ))
    and (p_price_min is null or p.price_cents >= p_price_min)
    and (p_price_max is null or p.price_cents <= p_price_max)
  order by p.created_at desc
  limit p_limit offset p_offset;
$$;
```

---
## Storage Buckets & Paths
- `products/` → gambar produk & thumbnails; contoh: `products/{productId}/{variantId}/preview.jpg`
- `models/` → model GLB/GLTF (Draco/KTX2); contoh: `models/{productId}/{variantId}/model.glb`
- `snapshots/` → hasil foto try‑on user: `snapshots/{userId}/{timestamp}.jpg`

Permissions:
- `products`, `models`: **public read**, write via service role / admin only.
- `snapshots`: **private** (read/write milik owner; public jika user pilih share).

---
## Indexing & Performance
- Index pada kolom filter umum: `products(active, created_at)`, `product_variants(product_id, active)`.
- Materialized view (opsional) untuk listing cepat (join varian & gambar).
