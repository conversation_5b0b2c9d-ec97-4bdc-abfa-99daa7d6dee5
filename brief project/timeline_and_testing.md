
# Timeline, Dependency Graph & Acceptance Test Checklist

Project: **Katalog Kacamata + Virtual Try-On (Next.js + Supabase + MediaPipe + Three.js)**

---

## 1) High-Level Timeline (6–8 Weeks)

> Estimasi konservatif untuk MVP; beberapa task dapat ber<PERSON>lan paralel. Gunakan sprint 1 minggu.

### Week 1 — Foundation & Environments
- Setup monorepo/app (Next.js + TS + Tailwind + shadcn/ui).
- Setup Supabase project, DB, RLS baseline.
- Auth wiring (helpers Next.js + Supabase).
- CI (lint/test/build) + Preview deploy (Vercel) + Env management.

### Week 2 — Catalog Data & Admin Seed
- Implement DB schema (products, variants, categories, assets).
- RLS policies publik/read-only, user-scope (wishlist/tryon_events).
- Seed data awal + upload contoh model GLB/preview images.
- Catalog list + filters (ISR) + detail product page.

### Week 3 — Try-On MVP
- Kamera permission + video feed wrapper.
- FaceMesh init + landmarks stream.
- Three.js scene + GLTF loader + penempatan frame dasar (posisi/scale).
- Stabilization/smoothing awal.

### Week 4 — UX Polish & Wishlist
- Variant selector (warna/ukuran) terintegrasi dengan model 3D.
- Wishlist (toggle, RLS-guarded) + account dashboard v1.
- Snapshot (download) + error states (izin kamera/WebGL unsupported).

### Week 5 — Performance & Storage
- Draco/KTX2 pipeline, model optimization.
- Throttle landmarks & EMA smoothing.
- Snapshot upload ke Supabase Storage (Edge Function optional).
- Observability (Sentry/Logs) + p95 target perf.

### Week 6 — Admin & Hardening
- Admin import (service role / Edge Function).
- Security pass (no service key on client, CORS, headers).
- Accessibility & responsive QA.
- UAT + bugfix + go-live checklist.

> Buffer Week 7–8 untuk konten, model 3D tambahan, dan refinements.

---

## 2) Dependency Graph

```mermaid
flowchart TD
  A[Repo & CI/CD] --> B[Supabase Setup + RLS]
  A --> C[Next.js Base + Auth Wiring]
  B --> D[DB Schema + Seed + Storage Buckets]
  C --> E[Catalog Pages (List/Detail)]
  D --> E
  E --> F[Variant Selector + Assets]
  C --> G[Try-On Pipeline MVP]
  G --> H[Three.js GLTF Integration]
  H --> I[Pose Smoothing & Calibration]
  F --> I
  I --> J[Snapshot Feature]
  E --> K[Wishlist + Account]
  D --> K
  I --> L[Perf Optimization (Draco/KTX2)]
  J --> M[Snapshot Upload (Edge Function)]
  K --> N[Admin Import & Tools]
  L --> O[QA/Accessibility/Hardening]
  M --> O
  N --> O
```

> Interpretasi:
> - **E** (Catalog) memerlukan **C** (Next.js base) dan **D** (DB/Seed).
> - **Try-On** jalur **G→H→I** bisa berjalan paralel setelah **C** siap.
> - **Wishlist (K)** butuh **E** dan **D** (RLS siap).
> - Finishing **O** menunggu optimasi/perf (L), snapshot upload (M), dan admin (N).

---

## 3) Parallelization Map (AIGent)

- Stream **Frontend-Catalog**: C → E → F → K
- Stream **Frontend-TryOn**: C → G → H → I → J → L
- Stream **Backend-Core**: B → D → RLS/Policies → Seed/Storage
- Stream **Edge/Tools**: M (Snapshot upload), N (Admin import), Observability
- Stream **QA/Release**: O (A11y, responsive, perf tests, UAT)

---

## 4) Detailed Deliverables per Week (DOD)

- **W1**: Build passes; Vercel preview; Supabase reachable; Auth sign-in/out ok.
- **W2**: Catalog REST/RPC stable; ISR listing <150ms p95; 10 produk seed; images served CDN.
- **W3**: Live landmarks 30 FPS target (mid device); GLTF renders aligned baseline.
- **W4**: Variant switch latency <150ms; wishlist toggles w/ RLS; snapshot download works.
- **W5**: GLB size < 2MB per variant (goal); FPS ≥24; logs visible; error states covered.
- **W6**: Admin import end-to-end; security review; A11y pass (keyboard/tab, contrast); UAT signoff.

---

## 5) Acceptance Test Checklist (Per Modul)

### A. Authentication
- [ ] Sign up/in/out bekerja di SSR & CSR (helpers nextjs).
- [ ] Cookie/session httpOnly (server) atau JS client terkelola; refresh token valid.
- [ ] Route proteksi `/account` & aksi wishlist memerlukan session.
- [ ] Tidak ada **service role key** muncul di bundle client.

### B. Database & RLS
- [ ] DDL sesuai `database.md`; constraints & indexes aktif.
- [ ] RLS katalog: **read-only publik**; write ditolak.
- [ ] RLS user-scope (wishlist/tryon_events): hanya owner bisa read/write.
- [ ] RPC `toggle_wishlist` bekerja atomik; `get_catalog` filter & paging benar.
- [ ] Backup & restore prosedur tervalidasi (smoke test).

### C. Catalog (Frontend)
- [ ] `/catalog` render < 1.5s TTI (4G); pagination/sorting stabil.
- [ ] Filter kategori/warna/harga berfungsi; URL state (query params) konsisten.
- [ ] `/product/[slug]` menampilkan varian + gallery (zoom/drag).
- [ ] Graceful loading (skeletons), error boundary pada fetch.

### D. Try-On (Frontend)
- [ ] Permission dialog jelas; fallback bila ditolak.
- [ ] FaceMesh berjalan real-time; landmark konsisten (no jitter berlebih).
- [ ] Penempatan kacamata: posisi, skala, rotasi selaras dengan wajah.
- [ ] Stabilization: EMA/kalman smoothing; tidak “melayang” saat kepala bergerak.
- [ ] Snapshot: hasil akurat (overlay + video), bisa diunduh.

### E. 3D Assets & Performance
- [ ] Model GLB ter-load via GLTFLoader; Draco/KTX2 aktif.
- [ ] Frame rate: ≥24 FPS mid device; CPU/GPU tidak throttle berlebihan.
- [ ] Tekstur ≤ 2K; total size per varian ≤ 2MB (goal).
- [ ] Material/lighting konsisten (no flicker); WebGL context loss ditangani.

### F. Wishlist & Account
- [ ] Toggle cepat (optimistic UI) + konsistensi setelah refresh.
- [ ] Daftar wishlist sesuai user; tidak terlihat oleh user lain (RLS).
- [ ] Account page menampilkan riwayat try-on (opsional) & profil dasar.

### G. Storage & Edge Functions
- [ ] Buckets: `products/`, `models/`, `snapshots/` dengan policy sesuai.
- [ ] Snapshot upload (optional edge): validasi mimetype, ukuran, path by userId.
- [ ] Admin import: hanya via service role; audit log tersedia.

### H. Security, A11y, Compliance
- [ ] Semua halaman via HTTPS; kamera hanya aktif saat halaman try-on fokus.
- [ ] CSP & headers dasar (no sniff, referrer, frame-ancestors).
- [ ] A11y: keyboard navigable, aria labels, contrast pass.
- [ ] Privacy notice untuk kamera/snapshot.

---

## 6) Release Checklist

- [ ] Domain + SSL OK; env vars Vercel/Supabase benar.
- [ ] Seed data final + model 3D teroptimasi.
- [ ] Sentry/Logs aktif; uptime monitor.
- [ ] Analytics dasar (page view, try-on start, snapshot click).
- [ ] Rollback plan & DB snapshot pre-release.

---

## 7) Risk & Mitigation

- **Perf device low-end** → Fallback foto & kurangi resolusi canvas/landmarks FPS.
- **Model 3D berat** → Draco/KTX2, simplify mesh, instancing.
- **Permission camera ditolak** → Jelaskan manfaat + tombol retry; mode foto.
- **SSR/CSR auth mismatch** → Gunakan auth-helpers resmi; integration tests.
- **RLS salah konfigurasi** → Uji dengan user A/B; negative tests (forbidden paths).

---

## 8) Ownership & Handover (AIGent)

- **FE-Catalog**: Layout, listing, detail, variant, wishlist UI.
- **FE-TryOn**: Camera wrapper, FaceMesh, Three.js, smoothing, snapshot.
- **BE-Core**: DB, RLS, RPC, Storage policies, Seed.
- **Edge/Tools**: Snapshot upload, Admin import, Observability.
- **QA/Release**: Test plans, a11y, security, UAT, go-live.

> Setiap stream melaporkan DOD mingguan + blocker; gunakan issue templates dan PR checklist mengacu ke Acceptance Test di atas.

