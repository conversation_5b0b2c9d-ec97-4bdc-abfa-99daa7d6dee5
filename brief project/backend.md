
# Backend Plan (Supabase: Postgres + Auth + Storage + Edge Functions)

## Goals
- Menyediakan API data produk, kategori, varian, aset 3D.
- Mengelola wishlist & riwayat try‑on per user.
- Menyajikan file GLB/GLTF dan gambar melalui Storage + CDN.

---
## Komponen Backend
- **Postgres (Supabase)** dengan **RLS** aktif.
- **Auth**: Supabase Auth (email/password + OAuth opsional).
- **Storage**: bucket `products`, `snapshots`, `models`.
- **Edge Functions** (opsional): 
  - `tryon-log` (catat performa/telemetri), 
  - `image-snapshot-upload` (validasi & resize),
  - `admin-import` (seed/batch import produk).

---
## API Surface
> Mengandalkan **Supabase auto-generated REST** (PostgREST) dan/atau **RPC (SQL function)** + **Edge Functions**.

- **REST** (table-based):
  - `GET /rest/v1/products?select=...&active=is.true`
  - `GET /rest/v1/product_variants?product_id=eq.{id}`
  - `GET /rest/v1/categories`
  - `GET /rest/v1/wishlist?user_id=eq.{uid}`
  - `POST /rest/v1/wishlist` (insert; RLS: user_id = auth.uid())
  - `DELETE /rest/v1/wishlist?id=eq.{id}&user_id=eq.{uid}`

- **RPC** (contoh):
  - `POST /rest/v1/rpc/get_catalog(p)` → filter, sort, paging terenkapsulasi.
  - `POST /rest/v1/rpc/toggle_wishlist(product_id uuid)` → atomic upsert/delete.
  - `POST /rest/v1/rpc/log_tryon(event jsonb)` → catat event anonymized.

- **Edge Functions** (opsional/keamanan tambahan):
  - `POST /functions/v1/snapshot` → verifikasi mimetype, simpan ke bucket `snapshots/uid/...`.
  - `POST /functions/v1/admin/seed` → proteksi via service role key.

---
## Observability & Ops
- **Row Level Security** wajib untuk semua tabel user-related.
- **Policies**: minimal, eksplisit, principle of least privilege.
- **Backups**: automatic daily.
- **Logs**: Edge Functions + DB logs; dashboard Supabase + Sentry (frontend).

---
## Acceptance Criteria (Backend)
- Semua endpoint publik hanya baca (produk/kategori).
- Semua endpoint user-scope mensyaratkan `auth.uid()`.
- Storage path terpisah per bucket & per user.
- Query katalog < 150ms p95 (indexing benar).

---
## Backlog Backend (Checklist untuk AIGent)
- [ ] Setup proyek Supabase.
- [ ] Buat skema DB + constraints + index.
- [ ] Aktifkan RLS + tulis policies.
- [ ] Buat RPC `toggle_wishlist`, `get_catalog`.
- [ ] Buat Edge Function `snapshot` (opsional).
- [ ] Seed data contoh + upload model GLB/preview images.
- [ ] Pengujian PostgREST & RLS end-to-end.
