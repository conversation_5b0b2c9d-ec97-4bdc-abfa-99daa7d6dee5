
# Frontend Plan (Next.js + Three.js + MediaPipe)

## Goals
- Katalog produk frame kacamata (UX premium ala Moscot).
- Fitur Virtual Try‑On (kamera real‑time, overlay 3D).
- Terintegrasi Supabase (Auth, DB, Storage).

---
## Tech Stack
- **Next.js 14+ (App Router)**
- **TypeScript**
- **Tailwind CSS + shadcn/ui**
- **Three.js** untuk render 3D (GLTF/GLB via `GLTFLoader`)
- **MediaPipe FaceMesh** (opsi: `@mediapipe/tasks-vision` atau `@tensorflow-models/face-landmarks-detection`)
- **Zustand** untuk state ringan (wishlist/cart/try-on state)
- **Supabase JS client** untuk akses DB/Storage/Auth

---
## IA & Routes (App Router)
- `/` — Landing: hero, koleksi unggulan, CTA ke katalog.
- `/catalog` — Grid produk + filter: kate<PERSON>i, bentuk, warna, harga.
- `/product/[slug]` — Detail produk: foto, varian (warna/ukuran), tombol **Try‑On**, wishlist.
- `/try-on/[productId]` — Halaman AR: kamera + overlay Three.js, pilih varian, snapshot.
- `/account` — Dashboard user (wishlist, riwayat try‑on, profil).
- `/auth/*` — Sign in/up/reset (handled by Supabase Auth UI atau custom form).
- `/admin/*` (opsional fase 2) — CRUD produk via dashboard admin.

---
## Komponen Utama
- `Header`, `Footer`, `Nav`, `Breadcrumbs`
- `ProductCard`, `ProductGrid`, `ProductFilters`
- `ProductGallery` (carousel + zoom)
- `VariantSelector` (warna/ukuran)
- `TryOnCanvas` (wadah WebGL + video feed)
- `PermissionDialog` (izin kamera)
- `WishlistButton` (toggle, requires auth)
- `SnapshotToolbar` (ambil foto, download/share)
- `Loader3D` (GLTF loader, progress)
- `Toaster` (notifikasi)

---
## Virtual Try‑On (Detail Frontend)
- **Pipeline**
  1. Minta izin kamera → tampilkan preview video.
  2. Inisialisasi FaceMesh → landmark 468 (wajah).
  3. Hitung transformasi (posisi, skala, rotasi) kacamata:
     - Gunakan titik kunci (contoh): 33 & 263 (outer eye corners) untuk skala, 168 (nose bridge), 1/2 untuk orientasi.
  4. Render objek GLTF kacamata pada `Scene` Three.js, sinkron dengan frame kamera (requestAnimationFrame).
  5. Kontrol UI: ganti varian (ubah material/mesh), toggle refleksi/lens opacity.

- **Optimasi**
  - GLTF: kompresi **Draco** + tekstur **KTX2**.
  - Kurangi polycount; batasi material multi-pass.
  - Throttle landmark update (mis. 30 FPS) → smoothing/exponential moving average untuk pose.
  - Canvas ukuran adaptif (mobile vs desktop).

- **Fallback**
  - Mode upload foto (non-realtime) untuk device lemah.

---
## State & Data Flows
- **Zustand stores**:
  - `useAuthStore` (session/user)
  - `useCatalogStore` (filters, sorting, pagination)
  - `useWishlistStore` (ids, optimistic toggle)
  - `useTryOnStore` (selectedProductId, variantId, tryOnSettings)

- **Fetching**:
  - Static generation untuk katalog (ISR 60–300s) + filter via server actions atau route handlers.
  - Detail produk `generateStaticParams` + revalidate.

---
## Integrasi Supabase
- Client instance pada **server components** (untuk SSR) dan **client components** (untuk interaksi).
- Gunakan **RLS**: hanya data publik (produk) yang terbuka; wishlist/user data butuh session.

---
## Acceptance Criteria (Frontend)
- Katalog tampil < 1.5s TTI pada koneksi 4G rata-rata.
- Try-On stabil ≥ 24 FPS pada perangkat mid-range (Chrome/Android/iOS modern).
- Snapshot menyimpan gambar lokal; opsi unggah ke Supabase Storage (opsional).
- Akses kamera jelas dan reversible (toggle off/on).
- Semua rute responsif (mobile‑first).

---
## Backlog Frontend (Checklist untuk AIGent)
- [x] Setup Next.js + TS + Tailwind + shadcn/ui.
- [x] Layout & navigasi dasar (Header/Footer/Nav).
- [x] Halaman `/catalog` + filter (SSR/ISR).
- [ ] Halaman `/product/[slug]` + gallery + variant selector.
- [x] Komponen `TryOnCanvas`: kamera + FaceMesh + Three.js.
- [x] Loader GLTF + material pipeline (Draco/KTX2).
- [x] Snapshot toolbar (canvas toDataURL, download).
- [ ] Autentikasi UI (signin/signup/reset).
- [ ] Wishlist (toggle, requires auth).
- [ ] Account dashboard (wishlist & try‑on history).
- [ ] Error boundaries (permission denied, WebGL unsupported).
